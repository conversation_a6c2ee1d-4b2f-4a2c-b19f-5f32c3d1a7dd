interface EventMap {
  sessionExpired: () => void;
  // 可以根据需要添加其他事件
}

class EventBus {
  private listeners: { [K in keyof EventMap]?: Array<EventMap[K]> } = {};

  on<K extends keyof EventMap>(event: K, listener: EventMap[K]): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event]!.push(listener);
  }

  off<K extends keyof EventMap>(event: K, listener: EventMap[K]): void {
    if (!this.listeners[event]) {
      return;
    }
    this.listeners[event] = this.listeners[event]!.filter(
      (l) => l !== listener
    );
  }

  emit<K extends keyof EventMap>(
    event: K,
    ...args: Parameters<EventMap[K]>
  ): void {
    if (!this.listeners[event]) {
      return;
    }
    // @ts-expect-error ts(2556) - 因为参数类型是联合类型，这里需要忽略
    this.listeners[event]!.forEach((listener) => listener(...args));
  }
}

const eventBus = new EventBus();

export default eventBus;
export type { EventMap };
