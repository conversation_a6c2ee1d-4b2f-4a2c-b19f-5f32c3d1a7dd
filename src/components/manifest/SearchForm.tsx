import React, {
  forwardRef,
  useImperativeHandle,
  ForwardRefRenderFunction,
  useState,
  useEffect,
  useCallback,
} from "react";
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  DatePicker,
  Select,
  Spin,
  Empty,
  Space,
  Collapse,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  DownOutlined,
} from "@ant-design/icons";
import dayjs, { Dayjs } from "dayjs";
import debounce from "lodash/debounce";
import { fetchUserOptions, UserOption } from "../../services/userService";
import {
  fetchMasterBillOptions,
  MasterBillOption,
} from "../../services/masterBillService";

const { RangePicker } = DatePicker;
const { Option } = Select;

export interface SearchFormValues {
  expressNumber?: string; // 快递单号
  transferredTrackingNumber?: string; // 转单号
  systemOrderNumber?: string; // 系统订单号
  merchantOrderNumber?: string; // 商家订单号
  dateType?: "createTime" | "pickUpTime" | "shipmentTime" | "deliveredTime";
  dateRange?: [dayjs.Dayjs, dayjs.Dayjs];
  userId?: number | null;
  masterBillId?: number | null;
  destinationCode?: string;
  cargoType?: number;
}

export interface SearchParams {
  expressNumber?: string; // 快递单号
  transferredTrackingNumber?: string; // 转单号
  systemOrderNumber?: string; // 系统订单号
  merchantOrderNumber?: string; // 商家订单号
  createTimeStart?: string;
  createTimeEnd?: string;
  pickUpTimeStart?: string;
  pickUpTimeEnd?: string;
  shipmentTimeStart?: string;
  shipmentTimeEnd?: string;
  deliveredTimeStart?: string;
  deliveredTimeEnd?: string;
  userId?: number;
  masterBillId?: number;
  destinationCode?: string;
  cargoType?: number;
}

export interface SearchFormRef {
  resetForm: () => void;
  submitForm: () => void;
}

interface SearchFormProps {
  onSearch: (params: SearchParams) => void;
  loading: boolean;
  setLoading?: (loading: boolean) => void;
  buttonPosition?: "right" | "none";
}

const ITEM_PAGE_SIZE = 20;

const SearchForm: ForwardRefRenderFunction<SearchFormRef, SearchFormProps> = (
  { onSearch, loading, setLoading, buttonPosition = "right" },
  ref
) => {
  const [form] = Form.useForm();
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [userLoading, setUserLoading] = useState<boolean>(false);
  const [userPage, setUserPage] = useState<number>(1);
  const [userTotal, setUserTotal] = useState<number>(0);
  const [userSearchKeyword, setUserSearchKeyword] = useState<string>("");
  const [masterBillOptions, setMasterBillOptions] = useState<
    MasterBillOption[]
  >([]);
  const [masterBillLoading, setMasterBillLoading] = useState<boolean>(false);
  const [masterBillPage, setMasterBillPage] = useState<number>(1);
  const [masterBillTotal, setMasterBillTotal] = useState<number>(0);
  const [masterBillSearchKeyword, setMasterBillSearchKeyword] =
    useState<string>("");
  const [, setAdvancedVisible] = useState<boolean>(false); // 高级查询展开状态

  const loadUserOptions = useCallback(
    async (keyword: string, page: number, append: boolean = false) => {
      setUserLoading(true);
      try {
        const apiResponse = await fetchUserOptions({
          keyword,
          page,
          pageSize: ITEM_PAGE_SIZE,
        });

        if (apiResponse.success && apiResponse.data) {
          const { list, total } = apiResponse.data;

          if (Array.isArray(list) && typeof total === "number") {
            setUserOptions((prevOptions) =>
              append ? [...prevOptions, ...list] : list
            );
            setUserTotal(total);
            setUserPage(page);
          } else {
            console.error(
              "Failed to load user options: Data structure inside ApiResponse.data is invalid.",
              apiResponse
            );
            if (!append) {
              setUserOptions([]);
              setUserTotal(0);
            }
          }
        } else {
          console.error(
            "Failed to load user options:",
            apiResponse.errorMessage || "API call failed or data was null"
          );
          if (!append) {
            setUserOptions([]);
            setUserTotal(0);
          }
        }
      } catch (error) {
        console.error(
          "Error fetching user options (network/exception):",
          error
        );
        if (!append) {
          setUserOptions([]);
          setUserTotal(0);
        }
      } finally {
        setUserLoading(false);
      }
    },
    []
  );

  const loadMasterBillOptions = useCallback(
    async (keyword: string, page: number, append: boolean = false) => {
      setMasterBillLoading(true);
      try {
        const apiResponse = await fetchMasterBillOptions({
          keyword,
          page,
          pageSize: ITEM_PAGE_SIZE,
        });

        if (apiResponse.success && apiResponse.data) {
          const { list, total } = apiResponse.data;

          if (Array.isArray(list) && typeof total === "number") {
            setMasterBillOptions((prevOptions) =>
              append ? [...prevOptions, ...list] : list
            );
            setMasterBillTotal(total);
            setMasterBillPage(page);
          } else {
            console.error(
              "Failed to load master bill options: Data structure inside ApiResponse.data is invalid.",
              apiResponse
            );
            if (!append) {
              setMasterBillOptions([]);
              setMasterBillTotal(0);
            }
          }
        } else {
          console.error(
            "Failed to load master bill options:",
            apiResponse.errorMessage || "API call failed or data was null"
          );
          if (!append) {
            setMasterBillOptions([]);
            setMasterBillTotal(0);
          }
        }
      } catch (error) {
        console.error(
          "Error fetching master bill options (network/exception):",
          error
        );
        if (!append) {
          setMasterBillOptions([]);
          setMasterBillTotal(0);
        }
      } finally {
        setMasterBillLoading(false);
      }
    },
    []
  );

  useEffect(() => {
    loadUserOptions("", 1);
    loadMasterBillOptions("", 1);
  }, [loadUserOptions, loadMasterBillOptions]);

  const debouncedUserSearch = useCallback(
    debounce((keyword: string) => {
      console.log(
        "[SearchForm] debouncedUserSearch triggered with keyword:",
        JSON.stringify(keyword)
      );
      setUserOptions([]);
      setUserPage(1);
      setUserSearchKeyword(keyword);
      loadUserOptions(keyword, 1, false);
    }, 500),
    [loadUserOptions]
  );

  const debouncedMasterBillSearch = useCallback(
    debounce((keyword: string) => {
      console.log(
        "[SearchForm] debouncedMasterBillSearch triggered with keyword:",
        JSON.stringify(keyword)
      );
      setMasterBillOptions([]);
      setMasterBillPage(1);
      setMasterBillSearchKeyword(keyword);
      loadMasterBillOptions(keyword, 1, false);
    }, 500),
    [loadMasterBillOptions]
  );

  const handleUserPopupScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const target = event.currentTarget;
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
      if (userOptions.length < userTotal && !userLoading) {
        loadUserOptions(userSearchKeyword, userPage + 1, true);
      }
    }
  };

  const handleMasterBillPopupScroll = (
    event: React.UIEvent<HTMLDivElement>
  ) => {
    const target = event.currentTarget;
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
      if (masterBillOptions.length < masterBillTotal && !masterBillLoading) {
        loadMasterBillOptions(
          masterBillSearchKeyword,
          masterBillPage + 1,
          true
        );
      }
    }
  };

  useImperativeHandle(ref, () => ({
    resetForm: () => {
      form.resetFields();
      debouncedUserSearch("");
      debouncedMasterBillSearch("");
      onSearch({});
    },
    submitForm: () => {
      if (setLoading) {
        setLoading(true);
      }
      handleFormSubmit();
    },
  }));

  const transformFormToParams = (values: SearchFormValues): SearchParams => {
    const {
      expressNumber,
      transferredTrackingNumber,
      systemOrderNumber,
      merchantOrderNumber,
      dateType,
      dateRange,
      userId,
      masterBillId,
      destinationCode,
      cargoType,
    } = values;
    const params: SearchParams = {};

    if (expressNumber) params.expressNumber = expressNumber;
    if (transferredTrackingNumber)
      params.transferredTrackingNumber = transferredTrackingNumber;
    if (systemOrderNumber) params.systemOrderNumber = systemOrderNumber;
    if (merchantOrderNumber) params.merchantOrderNumber = merchantOrderNumber;
    if (userId !== undefined && userId !== null) params.userId = userId;
    if (masterBillId !== undefined && masterBillId !== null)
      params.masterBillId = masterBillId;
    if (destinationCode) params.destinationCode = destinationCode;
    if (cargoType) params.cargoType = cargoType;

    if (dateRange && dateRange.length === 2) {
      const startDate = dateRange[0].format("YYYY-MM-DD");
      const endDate = dateRange[1].format("YYYY-MM-DD");
      switch (dateType) {
        case "createTime":
          params.createTimeStart = startDate;
          params.createTimeEnd = endDate;
          break;
        case "pickUpTime":
          params.pickUpTimeStart = startDate;
          params.pickUpTimeEnd = endDate;
          break;
        case "shipmentTime":
          params.shipmentTimeStart = startDate;
          params.shipmentTimeEnd = endDate;
          break;
        case "deliveredTime":
          params.deliveredTimeStart = startDate;
          params.deliveredTimeEnd = endDate;
          break;
        default:
          params.createTimeStart = startDate;
          params.createTimeEnd = endDate;
      }
    }
    return params;
  };

  const handleReset = () => {
    form.resetFields();
    debouncedUserSearch("");
    debouncedMasterBillSearch("");
    onSearch({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.value) {
      if (setLoading) setLoading(true);
      handleFormSubmit();
    }
  };

  const handleInputPressEnter = (e: React.KeyboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (setLoading) setLoading(true);
    handleFormSubmit();
  };

  const handleSelectEnter = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      if (setLoading) setLoading(true);
      handleFormSubmit();
    }
  };

  const handleFormSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const searchParams = transformFormToParams(values);
        onSearch(searchParams);
      })
      .catch((error) => {
        console.error("表单校验失败:", error);
        if (setLoading) setLoading(false);
      });
  };

  const handleSelectionChange = (
    fieldName: string,
    value?: number | string
  ) => {
    if (value === undefined) {
      if (fieldName === "userId") debouncedUserSearch("");
      if (fieldName === "masterBillId") debouncedMasterBillSearch("");
    }

    if (setLoading) {
      setLoading(true);
    }
    form
      .validateFields()
      .then((currentValues) => {
        const searchParams = transformFormToParams(currentValues);
        onSearch(searchParams);
      })
      .catch((error) => {
        console.error(`表单校验失败 (on${fieldName}Change):`, error);
        if (setLoading) {
          setLoading(false);
        }
      });
  };

  const handleDateRangeChange = (
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _dates: [Dayjs | null, Dayjs | null] | null,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _dateStrings: [string, string]
  ) => {
    // dates 和 dateStrings 参数保留但不直接使用，因为我们从表单值中获取数据
    if (setLoading) {
      setLoading(true);
    }
    form
      .validateFields()
      .then((currentValues) => {
        const searchParams = transformFormToParams(currentValues);
        onSearch(searchParams);
      })
      .catch((error) => {
        console.error("表单校验失败 (onDateRangeChange):", error);
        if (setLoading) {
          setLoading(false);
        }
      });
  };

  return (
    <Form
      form={form}
      layout="horizontal"
      initialValues={{ dateType: "createTime" }}
      onFinish={handleFormSubmit}
      className="search-form"
    >
      {/* 基础查询区域 */}
      {/* 第一行：快递单号、商家订单号、系统订单号、转单号 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6} lg={6} xl={6}>
          <Form.Item name="expressNumber" label="快递单号">
            <Input
              placeholder="输入快递单号"
              allowClear
              onChange={handleInputChange}
              onPressEnter={handleInputPressEnter}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6} xl={6}>
          <Form.Item name="merchantOrderNumber" label="商家订单号">
            <Input
              placeholder="输入商家订单号"
              allowClear
              onChange={handleInputChange}
              onPressEnter={handleInputPressEnter}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6} xl={6}>
          <Form.Item name="systemOrderNumber" label="系统订单号">
            <Input
              placeholder="输入系统订单号"
              allowClear
              onChange={handleInputChange}
              onPressEnter={handleInputPressEnter}
            />
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={6} lg={6} xl={6}>
          <Form.Item name="transferredTrackingNumber" label="转单号">
            <Input
              placeholder="输入转单号"
              allowClear
              onChange={handleInputChange}
              onPressEnter={handleInputPressEnter}
            />
          </Form.Item>
        </Col>
      </Row>

      {/* 第二行：客户筛选、日期类型、日期范围 */}
      <Row gutter={[16, 16]} style={{ marginTop: "8px" }}>
        <Col xs={24} sm={24} md={12} lg={8} xl={8}>
          <Form.Item name="userId" label="客户筛选">
            <Select
              placeholder="输入客户昵称/用户名搜索"
              allowClear
              showSearch
              filterOption={false}
              onSearch={debouncedUserSearch}
              loading={userLoading}
              onPopupScroll={handleUserPopupScroll}
              onChange={(value) => handleSelectionChange("userId", value)}
              notFoundContent={
                userLoading ? (
                  <Spin size="small" />
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="暂无数据"
                  />
                )
              }
            >
              {userOptions.map((user) => (
                <Option key={user.id} value={user.id} title={user.nickname}>
                  {user.nickname} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6} xl={6}>
          <Form.Item name="dateType" label="日期类型">
            <Select onKeyDown={handleSelectEnter}>
              <Option value="createTime">预报时间</Option>
              <Option value="pickUpTime">揽件时间</Option>
              <Option value="shipmentTime">发货时间</Option>
              <Option value="deliveredTime">送达时间</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} sm={12} md={16} lg={10} xl={10}>
          <Form.Item name="dateRange" label="日期范围">
            <RangePicker
              style={{ width: "100%" }}
              onChange={handleDateRangeChange}
            />
          </Form.Item>
        </Col>
        {buttonPosition === "right" && (
          <Col
            xs={24}
            sm={24}
            md={8}
            lg={6}
            xl={4}
            style={{ textAlign: "right", alignSelf: "flex-start" }}
          >
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  loading={loading}
                  htmlType="submit"
                >
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        )}
      </Row>

      {/* 高级查询折叠面板 */}
      <Collapse
        ghost
        expandIcon={({ isActive }) => (
          <DownOutlined rotate={isActive ? 180 : 0} />
        )}
        onChange={(keys) => setAdvancedVisible(keys.length > 0)}
        style={{ marginTop: "8px" }}
      >
        <Collapse.Panel header="高级查询" key="advanced">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6} lg={6} xl={6}>
              <Form.Item name="destinationCode" label="目的地">
                <Select
                  placeholder="选择目的地"
                  allowClear
                  onChange={(value) =>
                    handleSelectionChange("destinationCode", value)
                  }
                >
                  <Option value="1">东京</Option>
                  <Option value="2">大阪</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6} lg={6} xl={6}>
              <Form.Item name="cargoType" label="货物类型">
                <Select
                  placeholder="选择货物类型"
                  allowClear
                  onChange={(value) =>
                    handleSelectionChange("cargoType", value)
                  }
                >
                  <Option value={1}>普通</Option>
                  <Option value={2}>带电</Option>
                  <Option value={3}>投函</Option>
                  <Option value={6}>特殊</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={8} xl={8}>
              <Form.Item name="masterBillId" label="提单筛选">
                <Select
                  placeholder="输入提单号搜索"
                  allowClear
                  showSearch
                  filterOption={false}
                  onSearch={debouncedMasterBillSearch}
                  loading={masterBillLoading}
                  onPopupScroll={handleMasterBillPopupScroll}
                  onChange={(value) =>
                    handleSelectionChange("masterBillId", value)
                  }
                  notFoundContent={
                    masterBillLoading ? (
                      <Spin size="small" />
                    ) : (
                      <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description="暂无数据"
                      />
                    )
                  }
                >
                  {masterBillOptions.map((bill) => (
                    <Option
                      key={bill.id}
                      value={bill.id}
                      title={bill.masterBillNumber}
                    >
                      {bill.masterBillNumber}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
    </Form>
  );
};

export default forwardRef(SearchForm);
