# 账单导出功能说明

## 功能概述

新增了账单导出功能，允许用户将账单记录导出为 Excel 文件，包含完整的账单信息、运单明细和财务调整记录。

## 功能位置

### 1. 账单列表页面

- **位置**: `/billing-finance` 页面的操作列
- **按钮**: "导出" 按钮，带有下载图标
- **功能**: 点击后直接下载对应账单的 Excel 文件

### 2. 账单详情页面

- **位置**: `/billing-finance/detail/:id` 页面的头部操作区
- **按钮**: "导出账单" 按钮，主要样式，带有下载图标
- **功能**: 点击后下载当前查看账单的 Excel 文件

## 技术实现

### 后端接口

- **接口路径**: `POST /api/v1/finance/billing-record/export`
- **请求参数**: `{ billingRecordId: number }`
- **响应类型**: Excel 文件的二进制数据
- **文件名格式**: `{客户昵称}_账单_{账单编号}_{账单日期}.xlsx`

### 前端实现

#### 1. 服务层 (`billingService.ts`)

```typescript
// 导出账单请求参数接口
export interface ExportBillingRecordRequest {
  billingRecordId: number;
}

// 导出账单Excel文件
export const exportBillingRecord = async (billingRecordId: number): Promise<Blob>

// 下载账单Excel文件
export const downloadBillingRecord = async (billingRecordId: number, filename?: string): Promise<void>
```

#### 2. 组件实现

- **账单列表页面**: `BillManagementPage.tsx`
  - 在操作列添加导出按钮
  - 实现 `handleExportBilling` 函数
- **账单详情页面**: `BillDetailPage.tsx`
  - 在页面头部添加导出按钮
  - 实现 `handleExportBilling` 函数

#### 3. 工具函数 (`exportUtils.ts`)

```typescript
// 通用文件下载函数
export const downloadFile = (blob: Blob, filename: string): void

// 从响应头获取文件名
export const getFilenameFromContentDisposition = (contentDisposition: string): string | null

// 生成默认的Excel文件名
export const generateExcelFilename = (prefix: string, id: number): string
```

## Excel 文件内容

生成的 Excel 文件包含以下内容：

### 1. 文件信息

- **工作表名称**: "账单明细"
- **文件名格式**: `{客户昵称}_账单_{账单编号}_{账单日期}.xlsx`

### 2. 表头信息

- 公司标题：斑马物巢物流有限公司 - {客户昵称}账单明细
- 账单信息：账单编号、账期、总金额、状态等
- 模板信息：使用的运费模板详情（普通货物、带电货物、投函货物）

### 3. 运单明细部分

包含以下列：

- 序号、快递单号、系统单号、转单号
- 发货时间、预报时间、收件人、物品名称、货物类型
- 重量(kg)、长(cm)、宽(cm)、高(cm)、三边和(cm)、体积重(kg)、计费重量(kg)
- 首重费用(元)、续重费用(元)、超长费(元)、超重费(元)、偏远费(元)、其他费用(元)、总费用(元)

### 4. 财务调整明细部分

包含以下列：

- 序号、调整类型、调整描述、调整金额(元)、货币单位、生效日期
- 关联快递单号、关联系统单号、关联收件人、创建时间

### 5. 汇总信息

- 运单明细小计
- 财务调整小计
- 账单总计
- 报表生成时间

## 用户体验

### 1. 加载状态

- 点击导出按钮后显示 "正在生成 Excel 文件..." 的加载提示
- 使用 `message.loading` 提供用户反馈

### 2. 成功反馈

- 文件下载成功后显示 "账单导出成功" 的成功提示
- 文件自动下载到用户的默认下载目录

### 3. 错误处理

- 网络错误或服务器错误时显示具体的错误信息
- 无效的账单 ID 时显示 "无效的账单记录 ID" 错误

### 4. 文件命名

- 自动从服务器响应头获取文件名
- 如果无法获取，使用默认格式：`账单明细_{账单ID}.xlsx`

## 权限控制

- 需要有效的 JWT 认证 token
- 只能导出用户有权限查看的账单记录
- 遵循现有的权限控制机制

## 注意事项

1. **文件大小**: 大量明细数据可能导致较大的 Excel 文件
2. **网络超时**: 数据量大时可能需要较长的生成时间
3. **浏览器兼容性**: 使用现代浏览器的 Blob 和 URL API
4. **内存使用**: 文件在内存中生成，大文件时注意内存使用

## 测试建议

1. **功能测试**:

   - 测试不同大小的账单导出
   - 测试包含调整记录的账单导出
   - 测试空账单的导出

2. **错误测试**:

   - 测试无效账单 ID 的处理
   - 测试网络错误的处理
   - 测试权限不足的处理

3. **兼容性测试**:
   - 测试不同浏览器的下载行为
   - 测试文件名的中文字符处理

## 更新日志

- **2024-01-15**: 初始版本，支持账单 Excel 导出功能
  - 在账单列表和详情页面添加导出按钮
  - 实现完整的导出服务和错误处理
  - 添加用户友好的加载和反馈提示
