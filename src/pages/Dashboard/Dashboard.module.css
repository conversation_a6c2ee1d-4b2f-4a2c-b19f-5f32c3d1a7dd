/* src/pages/Dashboard/Dashboard.module.css */
.dashboardContainer {
  padding: 20px;
  background: #f0f2f5;
}

/* 调整 Row 之间的间距 */
.summaryRow,
.actionsRow,
.chartsRow {
  margin-bottom: 20px !important;
}

/* 卡片样式调整 */
:global(.ant-card) {
  border-radius: 8px; /* 给卡片加点圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); /* 轻微阴影 */
}

:global(.ant-card-head) {
  font-weight: 500; /* 卡片标题加粗一点 */
  min-height: 48px;
}
:global(.ant-card-body) {
  padding: 20px; /* 稍微调整内边距 */
}

/* 统计卡片中的描述文字 */
:global(.ant-statistic) + .ant-typography {
  display: block;
  margin-top: 8px;
}

/* 让卡片在某些 Row 中高度一致 */
.fullHeightCard {
  height: 100%;
}
.fullHeightCard :global(.ant-card-body) {
  height: calc(100% - 48px); /* 减去标题高度 */
  display: flex;
  flex-direction: column;
}
/* 让待办事项列表占据剩余空间 */
.fullHeightCard :global(.ant-spin-nested-loading) {
  flex-grow: 1;
  overflow: auto; /* 如果内容过多则滚动 */
}

/* 图表占位符样式 */
.chartPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px; /* Adjust as needed */
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  color: #8c8c8c;
}

.actionIcon {
  font-size: 18px;
  color: rgba(0, 0, 0, 0.65);
  transition: color 0.3s;
}
.actionIcon:hover {
  color: #1890ff;
}

.userInfo {
  cursor: pointer;
  padding: 0 12px;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s;
}
.userInfo:hover {
  background: rgba(0, 0, 0, 0.025);
}

.clickableCard {
  cursor: pointer;
  transition: box-shadow 0.3s ease-in-out;
}

.clickableCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments if needed */
@media (max-width: 768px) {
  .summaryRow .ant-col {
    margin-bottom: 16px;
  }
  .dashboardContainer {
    padding: 10px;
  }
}
