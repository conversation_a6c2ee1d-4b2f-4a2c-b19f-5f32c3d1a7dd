---
description: 
globs: 
alwaysApply: true
---
1. 通用错误 (例如，100xxx)
错误码	英文标识 (常量名)	中文描述	HTTP 状态码建议
100000	SUCCESS	操作成功	200
100001	ERROR_UNKNOWN	未知错误/系统繁忙	500
100002	ERROR_INVALID_PARAMETER	无效参数	400
100003	ERROR_PARAMETER_MISSING	缺少必要参数	400
100004	ERROR_UNAUTHORIZED	未授权/未登录	401
100005	ERROR_FORBIDDEN_ACCESS	无权限访问	403
100006	ERROR_RESOURCE_NOT_FOUND	请求的资源未找到	404
100007	ERROR_METHOD_NOT_ALLOWED	请求方法不允许	405
100008	ERROR_REQUEST_TOO_FREQUENT	请求过于频繁/限流	429
100009	ERROR_SERVICE_UNAVAILABLE	服务暂时不可用	503
100010	ERROR_TIMEOUT	请求超时	504
100011	ERROR_DATA_VALIDATION_FAILED	数据校验失败 (通用)	400/422
2. 用户与认证模块 (例如，101xxx)
错误码	英文标识 (常量名)	中文描述	HTTP 状态码建议
101001	ERROR_USER_NOT_FOUND	用户不存在	404
101002	ERROR_USER_PASSWORD_WRONG	用户名或密码错误	401
101003	ERROR_USER_ACCOUNT_LOCKED	用户账户已锁定	403
101004	ERROR_USER_ACCOUNT_DISABLED	用户账户已禁用	403
101005	ERROR_TOKEN_INVALID	Token 无效或已过期	401
101006	ERROR_USERNAME_ALREADY_EXISTS	用户名已存在	409
101007	ERROR_EMAIL_ALREADY_EXISTS	邮箱已存在	409
3. 运单模块 (shipments) (例如，201xxx)
错误码	英文标识 (常量名)	中文描述	HTTP 状态码建议
201001	ERROR_SHIPMENT_NOT_FOUND	运单不存在	404
201002	ERROR_SHIPMENT_STATUS_INVALID_OPERATION	当前运单状态不允许此操作	409 (Conflict)
201003	ERROR_SHIPMENT_WEIGHT_INVALID	运单重量无效 (如超出限制)	400
201004	ERROR_SHIPMENT_DIMENSIONS_INVALID	运单尺寸无效	400
201005	ERROR_SHIPMENT_RECEIVER_ADDRESS_INVALID	收件人地址信息不完整或无效	400
201006	ERROR_SHIPMENT_ITEM_PROHIBITED	运单包含违禁品	400
201007	ERROR_SHIPMENT_ALREADY_EXISTS	运单号已存在 (尝试创建重复时)	409
201008	ERROR_SHIPMENT_UPDATE_NOT_ALLOWED	运单信息不允许修改 (例如已发货)	403
4. 单号池与分配模块 (tracking_number_pool, channels, 预报错误处理) (例如，202xxx)
错误码	英文标识 (常量名)	中文描述	HTTP 状态码建议
202001	ERROR_TRACKING_NUMBER_NOT_FOUND_IN_POOL	预报单号在号池中不存在	400/404
202002	ERROR_TRACKING_NUMBER_NOT_AVAILABLE	预报单号不可用 (已被分配/禁用)	409
202003	ERROR_TRACKING_NUMBER_CHANNEL_NOT_FOUND	未找到匹配的运单号渠道	400/404
202004	ERROR_TRACKING_NUMBER_CHANNEL_INACTIVE	运单号渠道未启用	400
202005	ERROR_INSUFFICIENT_TRACKING_NUMBERS	所选渠道可用单号不足	409
202006	ERROR_PRE_REGISTRATION_FILE_INVALID_FORMAT	预报文件格式错误	400
202007	ERROR_PRE_REGISTRATION_ERROR_ITEM_NOT_FOUND	预报错误记录不存在	404
202008	ERROR_PRE_REGISTRATION_ITEM_ALREADY_RESOLVED	该预报错误记录已处理	409
5. 承运商与规则配置模块 (例如，203xxx)
错误码	英文标识 (常量名)	中文描述	HTTP 状态码建议
203001	ERROR_CARRIER_NOT_FOUND	承运商不存在	404
203002	ERROR_LOCATION_NOT_FOUND	地点不存在	404
203003	ERROR_SHIPMENT_TYPE_NOT_FOUND	货物类型不存在	404
203004	ERROR_PROHIBITED_KEYWORD_ALREADY_EXISTS	违禁词已存在	409
203005	ERROR_ITEM_NAME_MAPPING_ALREADY_EXISTS	物品名称映射已存在	409
6. 第三方服务/外部API调用 (例如，301xxx)
错误码	英文标识 (常量名)	中文描述	HTTP 状态码建议
301001	ERROR_CARRIER_API_CONNECTION_FAILED	连接承运商API失败	502/504
301002	ERROR_CARRIER_API_REQUEST_FAILED	调用承运商API请求失败	502
301003	ERROR_CARRIER_API_RESPONSE_INVALID	承运商API响应无效或解析错误	502
API 响应结构示例 (JSON):
当发生错误时，API 应该返回类似这样的结构：
{
  "success": false, // 明确表示操作失败
  "errorCode": 201006,
  "errorMessage": "运单包含违禁品: 物品 '打火机' 属于禁运范围。", // 动态生成的可读信息
  "requestId": "uuid-for-this-request", // 便于追踪日志
  "timestamp": "2024-05-17T10:30:00Z",
  "data": null, // 或可以包含一些辅助调试信息
  "errors": [ // (可选) 如果有多个字段校验错误，可以列出来
    {
      "field": "items[0].name",
      "message": "物品名称包含违禁词 '打火机'"
    }
  ]
}
Use code with caution.
Json
成功响应时：
{
  "success": true,
  "errorCode": 100000, // 或直接为 null/0 表示成功
  "errorMessage": "操作成功",
  "requestId": "uuid-for-this-request",
  "timestamp": "2024-05-17T10:35:00Z",
  "data": {
    // 具体的业务数据
    "shipmentId": "shp_123xyz"
  }
}
Use code with caution.
Json
实施建议：
定义常量/枚举
Use code with caution.
统一异常处理: 
文档化: 将所有错误码及其含义、可能的触发场景记录在 API 文档中，方便前端和第三方开发者对接。


逐步完善: 先定义核心模块和通用错误码，随着业务发展再逐步补充。