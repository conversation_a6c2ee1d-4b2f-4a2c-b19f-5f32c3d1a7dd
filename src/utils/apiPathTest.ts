/**
 * API路径处理函数测试
 *
 * 这个文件用于测试API路径处理函数在不同环境下的行为
 * 可以在控制台运行以下代码进行测试：
 *
 * import { testApiPaths } from './utils/apiPathTest';
 * testApiPaths();
 */

import { getApiPath } from "../services/apiPaths";

/**
 * 测试API路径处理函数
 */
export function testApiPaths(): void {
  console.group("API路径处理测试");

  // 测试不同格式的路径
  const testPaths = [
    "/manifests/search",
    "manifests/search",
    "/api/v1/manifests/search",
    "/api/manifests/search",
    "/admin-api/manifests/search",
    "http://localhost:8080/api/v1/manifests/search",
  ];

  // 测试每个路径
  testPaths.forEach((path) => {
    const result = getApiPath(path);
    console.log(`原始路径: "${path}" => 处理后: "${result}"`);
  });

  console.groupEnd();
}

/**
 * 测试不同环境下的API路径处理
 * @param baseUrl 模拟的VITE_API_BASE_URL环境变量值
 * @param isDev 模拟的开发环境标志
 */
export function mockEnvironmentTest(
  baseUrl: string | null,
  isDev: boolean
): void {
  console.group(`环境测试: baseUrl=${baseUrl || "未设置"}, isDev=${isDev}`);

  // 注意：无法在运行时修改import.meta.env，这里只是提供信息
  console.log("在实际环境中，路径会根据当前环境变量进行处理");
  console.log("开发环境: /api 前缀");
  console.log("生产环境: /admin-api 前缀");
  console.log("如果设置了VITE_API_BASE_URL，则优先使用该值");

  console.groupEnd();
}
