---
description: 
globs: 
alwaysApply: true
---
React 项目编码规范

1. 通用原则
可读性优先： 代码首先是写给人看的，其次才是给机器执行的。
一致性： 在整个项目中保持编码风格的一致性。
单一职责原则 (SRP)： 组件和函数应尽可能只做一件事。
DRY (Don't Repeat Yourself)： 避免重复代码，抽象出可复用的组件和逻辑。
KISS (Keep It Simple, Stupid)： 保持简单，避免不必要的复杂性。
遵循 React 官方文档和最佳实践。
2. 文件与目录结构
组件目录： 通常在 src/components/ 目录下按功能或类型组织。
页面/视图目录： 通常在 src/pages/ 或 src/views/ 目录下，每个页面一个目录。
复用逻辑 (Hooks, Utils)： 放在 src/hooks/, src/utils/ 等目录。
状态管理： 如果使用 Redux 等，放在 src/store/ 或 src/features/ (按功能模块组织)。
资源文件： 图片、字体等放在 src/assets/。
样式文件： 可以与组件放在一起（例如 Button/Button.module.css）或集中管理（例如 src/styles/）。
测试文件： 与被测试文件放在一起，使用 .test.js 或 .spec.js 后缀。
3. 命名规范
3.1 组件
使用 PascalCase (大驼峰命名法)。例如：UserProfile, OrderList。
组件名称应具有描述性，清晰表达其功能或内容。
高阶组件 (HOC) 可以使用 with 前缀，例如 withAuth(MyComponent)。
3.2 文件
组件文件：与组件名一致，使用 PascalCase。例如：UserProfile.js (或 .jsx, .tsx)。
非组件文件 (如工具函数、Hooks)：可以使用 camelCase 或 kebab-case，团队内统一。例如：apiClient.js, useFormValidation.js 或 string-utils.js。
样式文件：如果与组件关联，可以使用组件名。例如：UserProfile.module.css。
3.3 变量与常量
变量 (let, var - 尽量少用 var): 使用 camelCase。例如：userName, isLoading。
常量 (const):
对于基本类型或不可变引用，使用 camelCase。例如：const maxItems = 10;
对于模块级别的“硬编码”常量，可以使用 UPPER_SNAKE_CASE。例如：const API_BASE_URL = '...';
布尔类型的变量或 props，建议使用 is 或 has 前缀。例如：isLoading, hasError。
3.4 函数与方法
使用 camelCase。例如：fetchUserData(), handleSubmit()。
事件处理函数，建议使用 handle 前缀，后跟事件名或操作名。例如：handleClick(), handleInputChange(), handleSubmitForm()。
3.5 CSS 类名
如果使用全局 CSS，推荐使用 BEM (Block Element Modifier) 命名约定。例如：block__element--modifier (.button__icon--disabled)。
如果使用 CSS Modules，类名在模块内是局部的，可以使用更简洁的 camelCase。
如果使用 Styled Components 或其他 CSS-in-JS 方案，遵循其自身的命名约定。
4. 组件编写规范
4.1 组件类型 (函数组件优先)
优先使用函数组件 (Functional Components) 和 Hooks。
仅在需要特定生命周期方法（如 getDerivedStateFromError）或极少数优化场景（如 shouldComponentUpdate 的复杂实现，但通常 React.memo 和 useMemo/useCallback 能满足需求）时才考虑类组件 (Class Components)。
4.2 Props
清晰命名 Props，使其含义明确。
使用解构赋值获取 Props。例如：function MyComponent({ name, age }) { ... }
为 Props 提供默认值（使用默认参数或 defaultProps 静态属性，函数组件推荐默认参数）。
避免直接修改 Props，Props 是只读的。
4.3 State 与 Hooks
使用 useState 管理组件内部状态。
对于复杂状态逻辑或需要在多个组件间共享的状态，考虑使用 useReducer、Context API 或专门的状态管理库。
将相关的 state 和更新函数组织在一起。
自定义 Hooks (useMyHook) 用于封装可复用的有状态逻辑。
Hook 使用规则:
只在 React 函数组件或自定义 Hook 的顶层调用 Hook。
不要在循环、条件语句或嵌套函数中调用 Hook。
4.4 事件处理
事件处理函数作为 Props 传递时，命名以 on 开头，后跟事件名。例如：onClick, onChange, onSubmit。
在组件内部定义的事件处理函数，使用 handle 前缀。例如：const handleClick = () => { ... };
如果事件处理函数需要传递参数，使用箭头函数包裹。例如：<button onClick={() => handleDelete(id)}>Delete</button>。
4.5 条件渲染
对于简单的条件，使用三元运算符或逻辑与 (&&)。
{isLoggedIn ? <UserProfile /> : <LoginForm />}
{unreadMessages.length > 0 && <NotificationBadge count={unreadMessages.length} />}
Use code with caution.
Jsx
对于复杂的条件渲染，可以将 JSX 片段提取到变量或辅助函数中。
避免在 render 方法中进行过多的逻辑计算，将其移到组件主体或 useMemo。
4.6 列表渲染与 Keys
使用 map() 方法渲染列表。
为列表中的每一项提供一个稳定且唯一的 key prop。通常使用数据项的 ID。避免使用数组索引作为 key，除非列表是静态的且不会重新排序或过滤。
4.7 Fragments
当组件需要返回多个元素而不想添加额外的 DOM 节点时，使用 <React.Fragment> 或其简写形式 <></>。
4.8 代码结构与顺序 (函数组件内)
建议的顺序：
useState, useReducer
useContext
useRef
useEffect (有依赖项的先于无依赖项的，或按逻辑分组)
useMemo, useCallback
其他自定义 Hooks
事件处理函数 (handle...)
辅助渲染函数 (如果需要)
return JSX
4.9 PropTypes 或 TypeScript 类型
强烈建议为组件的 Props 定义类型，以提高代码健壮性和可维护性。
JavaScript 项目: 使用 prop-types 库。
import PropTypes from 'prop-types';
MyComponent.propTypes = {
  name: PropTypes.string.isRequired,
  age: PropTypes.number,
  onClick: PropTypes.func
};
Use code with caution.
JavaScript
TypeScript 项目: 直接使用 TypeScript 接口或类型别名定义 Props 类型。这是 TypeScript 的核心优势之一。
interface MyComponentProps {
  name: string;
  age?: number;
  onClick: () => void;
}
const MyComponent: React.FC<MyComponentProps> = ({ name, age, onClick }) => { ... };
Use code with caution.
TypeScript
5. JSX 规范
5.1 属性
HTML 属性使用 camelCase。例如：className, onClick, tabIndex。
自定义 Props 也使用 camelCase。
字符串属性使用双引号 ""。
JavaScript 表达式属性使用花括号 {}。
5.2 布尔属性
如果属性值为 true，可以省略值。例如：<MyInput disabled /> 等同于 <MyInput disabled={true} />。
如果属性值为 false，必须显式传递。例如：<MyInput disabled={false} />。
5.3 自闭合标签
没有子元素的组件或 HTML 标签应使用自闭合形式。例如：<MyImage src="..." />，<hr />。
5.4 多行 JSX
将多行 JSX 表达式用圆括号 () 包裹起来，以提高可读性并避免 ASI (Automatic Semicolon Insertion) 问题。
5.5 JavaScript 表达式
在 {} 中避免过于复杂的 JavaScript 逻辑。如果逻辑复杂，将其提取到组件主体中的变量或函数。
6. 样式 (CSS/SCSS/CSS-in-JS)
6.1 命名约定 (如 BEM)
如果使用全局 CSS 或预处理器 (如 SCSS)，推荐 BEM 以避免样式冲突。
6.2 CSS Modules 或 Styled Components
推荐使用 CSS Modules 或 CSS-in-JS 方案 (如 Styled Components, Emotion) 来实现组件级别的样式封装，避免全局样式污染。
CSS Modules: 文件名通常为 [ComponentName].module.css (或 .scss)，在 JS 中导入并使用。
Styled Components: 在 JS 文件中直接定义带样式的组件。
6.3 避免内联样式 (除非必要)
避免大量使用内联样式 (style={{ color: 'red' }})，因为它难以维护和复用。
内联样式适用于动态计算的样式或非常简单的、一次性的样式。
7. 状态管理 (如 Redux, Zustand, Context API)
7.1 Actions, Reducers, Selectors 命名
遵循库自身的推荐命名约定。
Actions (Redux): 类型名使用 UPPER_SNAKE_CASE，例如 FETCH_USER_SUCCESS。Action Creator 函数使用 camelCase。
Reducers (Redux): 保持 Reducer 纯净，只负责根据 Action 更新状态。
Selectors: 用于从 Store 中派生数据的函数，命名清晰，例如 selectUserName(state)。
7.2 模块化
按功能模块 (Feature) 组织状态管理代码，例如 Redux Toolkit 的 "ducks" 模式或按 feature slices。
8. 代码格式化与 Linting
8.1 Prettier
强制使用 Prettier 自动格式化代码，确保团队代码风格统一。
配置 Prettier 规则（例如行宽、分号、引号类型）并提交到版本库。
集成到 IDE 和 Git pre-commit hook 中。
8.2 ESLint
强制使用 ESLint 进行代码质量检查和风格规范。
配置 ESLint 规则（例如 eslint-plugin-react, eslint-plugin-react-hooks, eslint-plugin-jsx-a11y）。
解决 ESLint 报告的所有错误和警告。
集成到 IDE 和 Git pre-commit hook 中。
9. 注释规范
为复杂的逻辑、不直观的代码或重要的决策添加注释。
注释应解释“为什么”这样做，而不是简单地“怎么做”。
组件级别的注释可以解释其用途、Props 和主要行为。
保持注释的更新，避免过时的注释误导他人。
使用 // TODO: 或 // FIXME: 标记待办事项或需要修复的问题。
10. 其他最佳实践
性能优化:
使用 React.memo 优化不必要的函数组件重渲染。
使用 useMemo 和 useCallback 缓存计算结果和函数实例。
合理使用 key prop。
考虑代码分割 (Code Splitting) 和懒加载 (Lazy Loading) 来优化初始加载性能。
错误边界 (Error Boundaries): 为可能出错的组件树部分添加错误边界，以优雅地处理运行时错误。
可访问性 (a11y): 遵循 Web 可访问性指南，确保应用对所有用户友好（例如，使用语义化的 HTML, ARIA 属性, 键盘导航支持）。
API 请求: 封装 API 调用逻辑（例如在 src/services/ 或 src/api/ 目录中），使用 async/await 处理异步操作。考虑使用请求库如 axios。
环境变量: 使用 .env 文件管理不同环境（开发、测试、生产）的配置。

代码审查: 所有代码提交前都应进行代码审查。