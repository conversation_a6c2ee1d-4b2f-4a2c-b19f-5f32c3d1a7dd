/* 移除 Vite 默认样式 */
/* :root { ... } */
/* a { ... } */
/* a:hover { ... } */
/* body { ... } */ /* 移除默认 body 样式 */
/* h1 { ... } */
/* button { ... } */
/* button:hover { ... } */
/* button:focus, button:focus-visible { ... } */
/* @media (prefers-color-scheme: light) { ... } */

/* 基础样式重置和布局 */
html,
body,
#root {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100vh; /* 使用 vh 确保占满视口高度 */
  /* 更新字体栈，优先使用 PingFang SC 和 Microsoft YaHei */
  font-family: "PingFang SC", "Microsoft YaHei", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
    "Noto Color Emoji";
  background-color: #fff; /* 默认背景设为白色，避免深色闪烁 */
  letter-spacing: 0.3px; /* 全局字间距优化 */
}
