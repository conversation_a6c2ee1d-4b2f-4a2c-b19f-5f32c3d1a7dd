import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  DatePicker,
  InputNumber,
  message,
  Switch,
  AutoComplete,
  Spin,
  Select,
} from "antd";
import dayjs from "dayjs";
import {
  addCompensationAdjustment,
  CompensationAdjustmentPayload,
  addReassignmentAdjustment,
  addReturnAdjustment,
  ReturnAdjustmentPayload,
  addDestructionAdjustment,
  DestructionAdjustmentPayload,
} from "../../services/financialService";
import {
  searchManifestsByExpressNumber,
  ManifestSearchItem,
} from "../../services/manifestService";
import { apiClient } from "../../services/apiClient";
import { getApiPath } from "../../services/apiPaths";
import ImageUpload from "../upload/ImageUpload";
import { BusinessType } from "../../services/uploadService";

// 运单基础信息接口
interface ManifestBasicInfo {
  manifestId: number;
  trackingNumber: string;
  orderNumber: string;
  customerAccountId: number;
  customerNickname?: string;
  receiverName: string;
  receiverPhone: string;
  receiverAddress: string;
  receiverZipCode: string;
  createTime: string;
  cost?: number;
  overLengthSurcharge?: number;
  remoteAreaSurcharge?: number;
}

// 通用 Props 接口
interface BaseAdjustmentModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  manifestInfo?: ManifestBasicInfo;
}

// 赔偿模态框组件
export const CompensationModal: React.FC<BaseAdjustmentModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  manifestInfo,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [isFreightDeductionEnabled, setIsFreightDeductionEnabled] =
    useState<boolean>(true);
  const [isValueCompensationEnabled, setIsValueCompensationEnabled] =
    useState<boolean>(true);
  const [totalFreightDeductionAmount, setTotalFreightDeductionAmount] =
    useState<number>(0);
  const [totalValueCompensationAmount, setTotalValueCompensationAmount] =
    useState<number>(0);
  const [totalCompensationAmount, setTotalCompensationAmount] =
    useState<number>(0);

  // 计算费用金额
  const calculateCompensationAmounts = () => {
    if (!manifestInfo) return;

    const formValues = form.getFieldsValue();
    const currentFreightDeduction = formValues.isFreightDeduction || false;
    const currentValueCompensation = formValues.isValueCompensation || false;

    // 计算基础运费总额
    const baseCost =
      (manifestInfo.cost || 0) +
      (manifestInfo.overLengthSurcharge || 0) +
      (manifestInfo.remoteAreaSurcharge || 0);

    // 计算扣运费总额
    const calculatedFreightDeduction = currentFreightDeduction
      ? baseCost * ((formValues.freightDeductionPercentage || 0) / 100)
      : 0;
    setTotalFreightDeductionAmount(calculatedFreightDeduction);

    // 计算保价赔偿总额
    const calculatedValueCompensation = currentValueCompensation
      ? (formValues.cargoValue || 0) *
        ((formValues.valueCompensationPercentage || 0) / 100)
      : 0;
    setTotalValueCompensationAmount(calculatedValueCompensation);

    // 计算总赔偿金额
    const calculatedTotalCompensation =
      calculatedFreightDeduction + calculatedValueCompensation;
    setTotalCompensationAmount(calculatedTotalCompensation);

    // 更新表单数据
    form.setFieldsValue({
      totalFreightDeductionAmount: calculatedFreightDeduction.toFixed(2),
      totalValueCompensationAmount: calculatedValueCompensation.toFixed(2),
      totalCompensationAmount: calculatedTotalCompensation.toFixed(2),
    });
  };

  // 监听表单字段变化
  const handleFormValuesChange = (changedValues: Record<string, unknown>) => {
    if (
      "cargoValue" in changedValues ||
      "freightDeductionPercentage" in changedValues ||
      "valueCompensationPercentage" in changedValues
    ) {
      calculateCompensationAmounts();
    }
  };

  useEffect(() => {
    if (visible && manifestInfo) {
      // 计算基础运费总额
      const baseCost =
        (manifestInfo.cost || 0) +
        (manifestInfo.overLengthSurcharge || 0) +
        (manifestInfo.remoteAreaSurcharge || 0);

      form.setFieldsValue({
        manifestId: manifestInfo.manifestId,
        customerAccountId: manifestInfo.customerAccountId,
        currency: "CNY",
        isFreightDeduction: true,
        isValueCompensation: true,
        freightDeductionPercentage: 100,
        valueCompensationPercentage: 100,
        cargoValue: 0,
        totalFreightDeductionAmount: baseCost.toFixed(2),
        totalValueCompensationAmount: "0.00",
        totalCompensationAmount: baseCost.toFixed(2),
        effectiveDate: dayjs(),
      });

      setIsFreightDeductionEnabled(true);
      setIsValueCompensationEnabled(true);
      setTotalFreightDeductionAmount(baseCost);
      setTotalValueCompensationAmount(0);
      setTotalCompensationAmount(baseCost);
    }
  }, [visible, manifestInfo, form]);

  const handleSubmit = async () => {
    if (!manifestInfo) return;

    try {
      const values = await form.validateFields();

      // 验证数据
      if (
        values.isValueCompensation &&
        (!values.cargoValue || values.cargoValue <= 0)
      ) {
        message.error("如果需要赔偿货值，请输入大于0的货值");
        return;
      }

      if (
        values.isFreightDeduction &&
        (!totalFreightDeductionAmount || totalFreightDeductionAmount <= 0)
      ) {
        message.error("如果需要减免运费，请确保减免运费总额大于0");
        return;
      }

      if (!totalCompensationAmount || totalCompensationAmount <= 0) {
        message.error("总赔偿金额必须大于0");
        return;
      }

      setSubmitting(true);

      const payload: CompensationAdjustmentPayload = {
        manifestId: manifestInfo.manifestId,
        customerAccountId: manifestInfo.customerAccountId,
        isFreightDeduction: values.isFreightDeduction,
        freightDeductionPercentage: values.isFreightDeduction
          ? values.freightDeductionPercentage
          : undefined,
        totalFreightDeductionAmount: values.isFreightDeduction
          ? -Math.abs(totalFreightDeductionAmount)
          : undefined,
        isValueCompensation: values.isValueCompensation,
        cargoValue: values.isValueCompensation ? values.cargoValue : undefined,
        valueCompensationPercentage: values.isValueCompensation
          ? values.valueCompensationPercentage
          : undefined,
        totalValueCompensationAmount: values.isValueCompensation
          ? -Math.abs(totalValueCompensationAmount)
          : undefined,
        totalCompensationAmount: -Math.abs(totalCompensationAmount),
        description: values.description,
        effectiveDate: values.effectiveDate.format("YYYY-MM-DD"),
        currency: "CNY",
        proofOfValueImageUrls: values.proofOfValueImageUrls || [],
      };

      await addCompensationAdjustment(payload);
      message.success("赔偿调整添加成功");
      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      console.error("赔偿表单提交失败:", error);

      // 尝试提取API返回的错误信息
      let errorMessage = "赔偿信息提交失败，请重试";

      if (error && typeof error === "object") {
        // 检查是否有response数据
        if ("response" in error && error.response) {
          const response = error.response as {
            data?: {
              errorMessage?: string;
              message?: string;
            };
            status?: number;
            statusText?: string;
          };
          if (response.data?.errorMessage) {
            errorMessage = response.data.errorMessage;
          } else if (response.data?.message) {
            errorMessage = response.data.message;
          }
        }
        // 检查是否是网络错误或其他类型的错误
        else if ("message" in error && typeof error.message === "string") {
          errorMessage = error.message;
        }
      }

      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="添加赔偿"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={submitting}
      maskClosable={false}
      destroyOnClose
      width={900}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormValuesChange}
        style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}
      >
        {/* 运单信息显示 */}
        {manifestInfo && (
          <div
            style={{
              width: "100%",
              marginBottom: "16px",
              padding: "12px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
              border: "1px solid #d9d9d9",
            }}
          >
            <h4 style={{ margin: "0 0 8px 0", color: "#1890ff" }}>运单信息</h4>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "8px",
                fontSize: "13px",
              }}
            >
              <div>
                <strong>快递单号:</strong> {manifestInfo.trackingNumber}
              </div>
              <div>
                <strong>订单号:</strong> {manifestInfo.orderNumber}
              </div>
              <div>
                <strong>收件人:</strong> {manifestInfo.receiverName}
              </div>
              <div>
                <strong>联系电话:</strong> {manifestInfo.receiverPhone}
              </div>
              <div>
                <strong>邮编:</strong> {manifestInfo.receiverZipCode}
              </div>
              <div>
                <strong>客户:</strong>{" "}
                {manifestInfo.customerNickname ||
                  `ID: ${manifestInfo.customerAccountId}`}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>收件地址:</strong> {manifestInfo.receiverAddress}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>创建时间:</strong> {manifestInfo.createTime}
              </div>
            </div>
          </div>
        )}

        <Form.Item name="manifestId" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="customerAccountId" hidden>
          <Input />
        </Form.Item>

        {/* 左侧：选项和调整 */}
        <div style={{ flex: "1", minWidth: "420px" }}>
          <div style={{ display: "flex", gap: "24px", marginBottom: "16px" }}>
            <Form.Item
              name="isFreightDeduction"
              label="是否减免运费"
              valuePropName="checked"
              style={{ flex: "1", margin: 0 }}
            >
              <Switch
                checked={isFreightDeductionEnabled}
                onChange={(checked) => {
                  setIsFreightDeductionEnabled(checked);
                  form.setFieldsValue({ isFreightDeduction: checked });
                  calculateCompensationAmounts();
                }}
                checkedChildren="是"
                unCheckedChildren="否"
              />
            </Form.Item>

            <Form.Item
              name="isValueCompensation"
              label="是否赔偿货值"
              valuePropName="checked"
              style={{ flex: "1", margin: 0 }}
            >
              <Switch
                checked={isValueCompensationEnabled}
                onChange={(checked) => {
                  setIsValueCompensationEnabled(checked);
                  form.setFieldsValue({ isValueCompensation: checked });
                  calculateCompensationAmounts();
                }}
                checkedChildren="是"
                unCheckedChildren="否"
              />
            </Form.Item>

            <Form.Item
              name="effectiveDate"
              label="生效日期"
              rules={[{ required: true, message: "请选择生效日期" }]}
              style={{ flex: "1", margin: 0 }}
            >
              <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
            </Form.Item>
          </div>

          {isFreightDeductionEnabled && (
            <div style={{ display: "flex", gap: "24px", marginBottom: "16px" }}>
              <Form.Item
                name="freightDeductionPercentage"
                label="减免运费比例 (%)"
                rules={[
                  {
                    required: isFreightDeductionEnabled,
                    message: "请输入减免运费比例",
                  },
                  {
                    type: "number",
                    min: 0,
                    max: 100,
                    message: "请输入0-100之间的数字",
                  },
                ]}
                style={{ flex: "1", margin: 0 }}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  min={0}
                  max={100}
                  precision={0}
                  disabled={!isFreightDeductionEnabled}
                />
              </Form.Item>
              <Form.Item
                name="totalFreightDeductionAmount"
                label="减免运费总额"
                style={{ flex: "1", margin: 0 }}
              >
                <InputNumber style={{ width: "100%" }} disabled prefix="¥" />
              </Form.Item>
            </div>
          )}

          {isValueCompensationEnabled && (
            <>
              <div
                style={{ display: "flex", gap: "24px", marginBottom: "16px" }}
              >
                <Form.Item
                  name="cargoValue"
                  label="货值"
                  rules={[
                    {
                      required: isValueCompensationEnabled,
                      message: "请输入货值",
                    },
                    { type: "number", min: 0.01, message: "货值必须大于0" },
                  ]}
                  style={{ flex: "1", margin: 0 }}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    min={0}
                    precision={2}
                    prefix="¥"
                    disabled={!isValueCompensationEnabled}
                  />
                </Form.Item>
                <Form.Item
                  name="valueCompensationPercentage"
                  label="货值赔偿比例 (%)"
                  rules={[
                    {
                      required: isValueCompensationEnabled,
                      message: "请输入货值赔偿比例",
                    },
                    {
                      type: "number",
                      min: 0,
                      max: 100,
                      message: "请输入0-100之间的数字",
                    },
                  ]}
                  style={{ flex: "1", margin: 0 }}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    min={0}
                    max={100}
                    precision={0}
                    disabled={!isValueCompensationEnabled}
                  />
                </Form.Item>
                <Form.Item
                  name="totalValueCompensationAmount"
                  label="货值赔偿总额"
                  style={{ flex: "1", margin: 0 }}
                >
                  <InputNumber style={{ width: "100%" }} disabled prefix="¥" />
                </Form.Item>
              </div>
            </>
          )}

          <Form.Item
            name="totalCompensationAmount"
            label="总赔偿金额"
            style={{ marginBottom: "16px" }}
          >
            <InputNumber
              style={{ width: "100%", fontSize: "18px", fontWeight: "bold" }}
              disabled
              prefix="¥"
            />
          </Form.Item>
        </div>

        {/* 右侧：描述和证明 */}
        <div style={{ flex: "1", minWidth: "420px" }}>
          <Form.Item name="description" label="赔偿说明/备注">
            <Input.TextArea
              rows={3}
              placeholder="请简要说明赔偿原因和计算方式"
            />
          </Form.Item>

          <Form.Item name="proofOfValueImageUrls" label="货值证明图片">
            <ImageUpload
              businessType={BusinessType.COMPENSATION_PROOF}
              maxCount={5}
              placeholder="上传货值证明图片"
            />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
};

// 改派模态框组件
export const ReassignmentModal: React.FC<BaseAdjustmentModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  manifestInfo,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);

  useEffect(() => {
    if (visible && manifestInfo) {
      form.setFieldsValue({
        manifestId: manifestInfo.manifestId,
        customerAccountId: manifestInfo.customerAccountId,
        currency: "CNY",
        amount: 80, // 改派默认费用80元
        reassignmentNumber: "",
        effectiveDate: dayjs(),
        description: "",
      });
    }
  }, [visible, manifestInfo, form]);

  const handleSubmit = async () => {
    if (!manifestInfo) return;

    try {
      const values = await form.validateFields();

      if (!values.amount || values.amount <= 0) {
        message.error("改派费用必须大于0");
        return;
      }

      setSubmitting(true);

      const payload = {
        ...values,
        effectiveDate: values.effectiveDate.format("YYYY-MM-DD"),
        currency: "CNY", // 默认货币为人民币
      };

      await addReassignmentAdjustment(payload);
      message.success("改派调整添加成功");
      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      console.error("改派表单提交失败:", error);

      // 尝试提取API返回的错误信息
      let errorMessage = "改派信息提交失败，请重试";

      if (error && typeof error === "object") {
        // 检查是否有response数据
        if ("response" in error && error.response) {
          const response = error.response as {
            data?: {
              errorMessage?: string;
              message?: string;
            };
            status?: number;
            statusText?: string;
          };
          if (response.data?.errorMessage) {
            errorMessage = response.data.errorMessage;
          } else if (response.data?.message) {
            errorMessage = response.data.message;
          }
        }
        // 检查是否是网络错误或其他类型的错误
        else if ("message" in error && typeof error.message === "string") {
          errorMessage = error.message;
        }
      }

      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="添加改派调整"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={submitting}
      maskClosable={false}
      destroyOnClose
      width={700}
    >
      <Form form={form} layout="vertical">
        {/* 运单信息显示 */}
        {manifestInfo && (
          <div
            style={{
              marginBottom: "16px",
              padding: "12px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
              border: "1px solid #d9d9d9",
            }}
          >
            <h4 style={{ margin: "0 0 8px 0", color: "#1890ff" }}>运单信息</h4>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "8px",
                fontSize: "13px",
              }}
            >
              <div>
                <strong>快递单号:</strong> {manifestInfo.trackingNumber}
              </div>
              <div>
                <strong>订单号:</strong> {manifestInfo.orderNumber}
              </div>
              <div>
                <strong>收件人:</strong> {manifestInfo.receiverName}
              </div>
              <div>
                <strong>联系电话:</strong> {manifestInfo.receiverPhone}
              </div>
              <div>
                <strong>邮编:</strong> {manifestInfo.receiverZipCode}
              </div>
              <div>
                <strong>客户:</strong>{" "}
                {manifestInfo.customerNickname ||
                  `ID: ${manifestInfo.customerAccountId}`}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>收件地址:</strong> {manifestInfo.receiverAddress}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>创建时间:</strong> {manifestInfo.createTime}
              </div>
            </div>
          </div>
        )}

        <Form.Item name="manifestId" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="customerAccountId" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="currency" hidden>
          <Input />
        </Form.Item>

        <div style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}>
          <div style={{ flex: "1", minWidth: "320px" }}>
            <Form.Item
              name="reassignmentNumber"
              label="改派单号"
              rules={[{ required: true, message: "请输入改派单号" }]}
            >
              <Input placeholder="请输入改派单号" />
            </Form.Item>

            <div style={{ display: "flex", gap: "16px" }}>
              <Form.Item
                name="amount"
                label="改派费用"
                rules={[
                  { required: true, message: "请输入改派费用" },
                  { type: "number", min: 0.01, message: "改派费用必须大于0" },
                ]}
                style={{ flex: "1" }}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  prefix="¥"
                  precision={2}
                  min={0.01}
                />
              </Form.Item>

              <Form.Item
                name="effectiveDate"
                label="生效日期"
                rules={[{ required: true, message: "请选择生效日期" }]}
                style={{ flex: "1" }}
              >
                <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
              </Form.Item>
            </div>
          </div>

          <div style={{ flex: "1", minWidth: "320px" }}>
            <Form.Item name="description" label="改派原因/备注">
              <Input.TextArea
                rows={3}
                placeholder="请简要说明改派原因或相关情况"
              />
            </Form.Item>
          </div>
        </div>
      </Form>
    </Modal>
  );
};

// 销毁模态框组件
export const DestructionModal: React.FC<BaseAdjustmentModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  manifestInfo,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);

  useEffect(() => {
    if (visible && manifestInfo) {
      form.setFieldsValue({
        manifestId: manifestInfo.manifestId,
        customerAccountId: manifestInfo.customerAccountId,
        currency: "CNY",
        amount: 20, // 销毁默认费用20元
        effectiveDate: dayjs(),
        description: "",
      });
    }
  }, [visible, manifestInfo, form]);

  const handleSubmit = async () => {
    if (!manifestInfo) return;

    try {
      const values = await form.validateFields();

      if (!values.amount || values.amount <= 0) {
        message.error("销毁费用必须大于0");
        return;
      }

      setSubmitting(true);

      const payload: DestructionAdjustmentPayload = {
        manifestId: manifestInfo.manifestId,
        customerAccountId: manifestInfo.customerAccountId,
        amount: values.amount,
        description: values.description,
        effectiveDate: values.effectiveDate.format("YYYY-MM-DD"),
        currency: "CNY",
      };

      await addDestructionAdjustment(payload);
      message.success("销毁调整添加成功");
      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      console.error("销毁表单提交失败:", error);

      // 尝试提取API返回的错误信息
      let errorMessage = "销毁信息提交失败，请重试";

      if (error && typeof error === "object") {
        // 检查是否有response数据
        if ("response" in error && error.response) {
          const response = error.response as {
            data?: {
              errorMessage?: string;
              message?: string;
            };
            status?: number;
            statusText?: string;
          };
          if (response.data?.errorMessage) {
            errorMessage = response.data.errorMessage;
          } else if (response.data?.message) {
            errorMessage = response.data.message;
          }
        }
        // 检查是否是网络错误或其他类型的错误
        else if ("message" in error && typeof error.message === "string") {
          errorMessage = error.message;
        }
      }

      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="添加销毁"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={submitting}
      maskClosable={false}
      destroyOnClose
      width={600}
    >
      <Form form={form} layout="vertical">
        {/* 运单信息显示 */}
        {manifestInfo && (
          <div
            style={{
              marginBottom: "16px",
              padding: "12px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
              border: "1px solid #d9d9d9",
            }}
          >
            <h4 style={{ margin: "0 0 8px 0", color: "#1890ff" }}>运单信息</h4>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "8px",
                fontSize: "13px",
              }}
            >
              <div>
                <strong>快递单号:</strong> {manifestInfo.trackingNumber}
              </div>
              <div>
                <strong>订单号:</strong> {manifestInfo.orderNumber}
              </div>
              <div>
                <strong>收件人:</strong> {manifestInfo.receiverName}
              </div>
              <div>
                <strong>联系电话:</strong> {manifestInfo.receiverPhone}
              </div>
              <div>
                <strong>邮编:</strong> {manifestInfo.receiverZipCode}
              </div>
              <div>
                <strong>客户:</strong>{" "}
                {manifestInfo.customerNickname ||
                  `ID: ${manifestInfo.customerAccountId}`}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>收件地址:</strong> {manifestInfo.receiverAddress}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>创建时间:</strong> {manifestInfo.createTime}
              </div>
            </div>
          </div>
        )}

        <Form.Item name="manifestId" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="customerAccountId" hidden>
          <Input />
        </Form.Item>

        <div style={{ display: "flex", gap: "24px" }}>
          <Form.Item
            name="amount"
            label="销毁费用"
            rules={[
              { required: true, message: "请输入销毁费用" },
              { type: "number", min: 0.01, message: "销毁费用必须大于0" },
            ]}
            style={{ flex: "1" }}
          >
            <InputNumber
              style={{ width: "100%" }}
              min={0}
              precision={2}
              prefix="¥"
            />
          </Form.Item>

          <Form.Item
            name="effectiveDate"
            label="生效日期"
            rules={[{ required: true, message: "请选择生效日期" }]}
            style={{ flex: "1" }}
          >
            <DatePicker style={{ width: "100%" }} />
          </Form.Item>
        </div>

        <Form.Item name="description" label="销毁原因/备注">
          <Input.TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

// 退回模态框组件
export const ReturnModal: React.FC<BaseAdjustmentModalProps> = ({
  visible,
  onCancel,
  onSuccess,
  manifestInfo,
}) => {
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState<boolean>(false);

  useEffect(() => {
    if (visible && manifestInfo) {
      form.setFieldsValue({
        manifestId: manifestInfo.manifestId,
        customerAccountId: manifestInfo.customerAccountId,
        currency: "CNY",
        amount: 80, // 退回默认费用80元
        effectiveDate: dayjs(),
        description: "",
      });
    }
  }, [visible, manifestInfo, form]);

  const handleSubmit = async () => {
    if (!manifestInfo) return;

    try {
      const values = await form.validateFields();

      if (!values.amount || values.amount <= 0) {
        message.error("退回金额必须大于0");
        return;
      }

      setSubmitting(true);

      const payload: ReturnAdjustmentPayload = {
        manifestId: manifestInfo.manifestId,
        customerAccountId: manifestInfo.customerAccountId,
        amount: values.amount,
        description: values.description,
        effectiveDate: values.effectiveDate.format("YYYY-MM-DD"),
        currency: "CNY",
      };

      await addReturnAdjustment(payload);
      message.success("退回调整添加成功");
      form.resetFields();
      onSuccess();
    } catch (error: unknown) {
      console.error("退回表单提交失败:", error);

      // 尝试提取API返回的错误信息
      let errorMessage = "退回信息提交失败，请重试";

      if (error && typeof error === "object") {
        // 检查是否有response数据
        if ("response" in error && error.response) {
          const response = error.response as {
            data?: {
              errorMessage?: string;
              message?: string;
            };
            status?: number;
            statusText?: string;
          };
          if (response.data?.errorMessage) {
            errorMessage = response.data.errorMessage;
          } else if (response.data?.message) {
            errorMessage = response.data.message;
          }
        }
        // 检查是否是网络错误或其他类型的错误
        else if ("message" in error && typeof error.message === "string") {
          errorMessage = error.message;
        }
      }

      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="添加退回"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={submitting}
      maskClosable={false}
      destroyOnClose
      width={700}
    >
      <Form form={form} layout="vertical">
        {/* 运单信息显示 */}
        {manifestInfo && (
          <div
            style={{
              marginBottom: "16px",
              padding: "12px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
              border: "1px solid #d9d9d9",
            }}
          >
            <h4 style={{ margin: "0 0 8px 0", color: "#1890ff" }}>运单信息</h4>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "8px",
                fontSize: "13px",
              }}
            >
              <div>
                <strong>快递单号:</strong> {manifestInfo.trackingNumber}
              </div>
              <div>
                <strong>订单号:</strong> {manifestInfo.orderNumber}
              </div>
              <div>
                <strong>收件人:</strong> {manifestInfo.receiverName}
              </div>
              <div>
                <strong>联系电话:</strong> {manifestInfo.receiverPhone}
              </div>
              <div>
                <strong>邮编:</strong> {manifestInfo.receiverZipCode}
              </div>
              <div>
                <strong>客户:</strong>{" "}
                {manifestInfo.customerNickname ||
                  `ID: ${manifestInfo.customerAccountId}`}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>收件地址:</strong> {manifestInfo.receiverAddress}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>创建时间:</strong> {manifestInfo.createTime}
              </div>
            </div>
          </div>
        )}

        <Form.Item name="manifestId" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="customerAccountId" hidden>
          <Input />
        </Form.Item>

        <div style={{ display: "flex", gap: "16px", marginBottom: "16px" }}>
          <Form.Item
            name="amount"
            label="退回金额"
            rules={[
              { required: true, message: "请输入退回金额" },
              { type: "number", min: 0.01, message: "退回金额必须大于0" },
            ]}
            style={{ flex: "1" }}
          >
            <InputNumber
              style={{ width: "100%" }}
              min={0.01}
              step={0.01}
              precision={2}
              prefix="¥"
            />
          </Form.Item>

          <Form.Item
            name="effectiveDate"
            label="生效日期"
            rules={[{ required: true, message: "请选择生效日期" }]}
            style={{ flex: "1" }}
          >
            <DatePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
          </Form.Item>
        </div>

        <Form.Item name="description" label="退回原因/备注">
          <Input.TextArea rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  );
};

// 运单选择模态框组件
export const ManifestSelectorModal: React.FC<{
  visible: boolean;
  onCancel: () => void;
  onSelect: (manifestInfo: ManifestBasicInfo) => void;
}> = ({ visible, onCancel, onSelect }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<ManifestSearchItem[]>([]);
  const [selectedManifest, setSelectedManifest] =
    useState<ManifestSearchItem | null>(null);

  // 搜索运单
  const handleSearch = async (value: string) => {
    if (!value || value.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      setSearchLoading(true);
      const results = await searchManifestsByExpressNumber({
        expressNumber: value,
        page: 1,
        pageSize: 10,
      });
      setSearchResults(results);
    } catch (error) {
      console.error("搜索运单失败:", error);
      message.error("搜索运单失败");
      setSearchResults([]);
    } finally {
      setSearchLoading(false);
    }
  };

  // 选择运单
  const handleSelectManifest = (value: string) => {
    const manifest = searchResults.find((m) => m.expressNumber === value);
    if (manifest) {
      setSelectedManifest(manifest);
      form.setFieldValue("selectedManifest", manifest.expressNumber);
    }
  };

  // 处理提交
  const handleSubmit = async () => {
    try {
      await form.validateFields();

      if (!selectedManifest) {
        message.error("请选择一个运单");
        return;
      }

      setLoading(true);

      // 转换为 ManifestBasicInfo 格式
      const manifestInfo: ManifestBasicInfo = {
        manifestId: selectedManifest.id,
        trackingNumber: selectedManifest.expressNumber,
        orderNumber: selectedManifest.orderNumber,
        customerAccountId: selectedManifest.userId,
        customerNickname: selectedManifest.userNickname,
        receiverName: selectedManifest.receiverName,
        receiverPhone: selectedManifest.receiverPhone,
        receiverAddress: selectedManifest.receiverAddress,
        receiverZipCode: selectedManifest.receiverZipCode,
        createTime: selectedManifest.createTime,
        cost: 40, // 默认费用，实际项目中应该从运单详情获取
        overLengthSurcharge: 0,
        remoteAreaSurcharge: 0,
      };

      onSelect(manifestInfo);
      form.resetFields();
      setSelectedManifest(null);
      setSearchResults([]);
    } catch (error) {
      console.error("选择运单失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedManifest(null);
    setSearchResults([]);
    onCancel();
  };

  // 构建自动完成选项
  const autoCompleteOptions = searchResults.map((manifest) => ({
    value: manifest.expressNumber,
    label: (
      <div style={{ padding: "8px 0" }}>
        <div style={{ fontWeight: "bold", fontSize: "14px" }}>
          {manifest.expressNumber}
        </div>
        <div style={{ color: "#666", fontSize: "12px" }}>
          收件人: {manifest.receiverName} | 客户: {manifest.userNickname}
        </div>
        <div style={{ color: "#999", fontSize: "12px" }}>
          地址: {manifest.receiverAddress} | 邮编: {manifest.receiverZipCode}
        </div>
      </div>
    ),
    manifest: manifest,
  }));

  return (
    <Modal
      title="选择运单"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      maskClosable={false}
      destroyOnClose
      width={600}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="selectedManifest"
          label="搜索运单"
          rules={[{ required: true, message: "请选择一个运单" }]}
        >
          <AutoComplete
            placeholder="请输入快递单号进行搜索"
            onSearch={handleSearch}
            onSelect={handleSelectManifest}
            options={autoCompleteOptions}
            notFoundContent={
              searchLoading ? <Spin size="small" /> : "暂无匹配结果"
            }
            filterOption={false}
            style={{ width: "100%" }}
          />
        </Form.Item>

        {/* 显示选中的运单详情 */}
        {selectedManifest && (
          <div
            style={{
              marginTop: "16px",
              padding: "12px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
              border: "1px solid #d9d9d9",
            }}
          >
            <h4 style={{ margin: "0 0 8px 0", color: "#1890ff" }}>
              已选运单信息
            </h4>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "1fr 1fr",
                gap: "8px",
                fontSize: "13px",
              }}
            >
              <div>
                <strong>快递单号:</strong> {selectedManifest.expressNumber}
              </div>
              <div>
                <strong>订单号:</strong> {selectedManifest.orderNumber}
              </div>
              <div>
                <strong>收件人:</strong> {selectedManifest.receiverName}
              </div>
              <div>
                <strong>联系电话:</strong> {selectedManifest.receiverPhone}
              </div>
              <div>
                <strong>邮编:</strong> {selectedManifest.receiverZipCode}
              </div>
              <div style={{ gridColumn: "1 / -1" }}>
                <strong>收件地址:</strong> {selectedManifest.receiverAddress}
              </div>
              <div>
                <strong>客户:</strong> {selectedManifest.userNickname}
              </div>
              <div>
                <strong>创建时间:</strong> {selectedManifest.createTime}
              </div>
            </div>
          </div>
        )}
      </Form>
    </Modal>
  );
};

// 批量调整接口
interface BatchAdjustmentItem {
  adjustmentType: string;
  amount: number;
}

interface BatchAdjustmentRequest {
  trackingNumbers: string[];
  adjustmentItems: BatchAdjustmentItem[];
  effectiveDate: string;
  description?: string;
  currency: string;
}

interface BatchAdjustmentResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  data: {
    successCount: number;
    failureCount: number;
    totalCount: number;
    successItems: Record<string, unknown>[];
    failureItems: {
      trackingNumber: string;
      adjustmentType: string;
      errorCode: number;
      errorMessage: string;
    }[];
    summary: {
      totalAmount: number;
      reassignmentCount: number;
      returnCount: number;
      destructionCount: number;
    };
  };
}

// 表单数据类型
interface BatchFormData {
  trackingNumbers: string;
  adjustmentItems: Array<{
    adjustmentType: string;
    amount: number;
    enabled: boolean;
  }>;
  effectiveDate: dayjs.Dayjs;
  description?: string;
  currency: string;
}

// 批量添加模态框组件
export const BatchAddModal: React.FC<{
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}> = ({ visible, onCancel, onSuccess }) => {
  const [form] = Form.useForm<BatchFormData>();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [modal, contextHolder] = Modal.useModal();

  // 默认调整类型和金额
  const defaultAdjustmentTypes = [
    { type: "DESTRUCTION", label: "销毁", defaultAmount: 20 },
    { type: "RETURN", label: "退回", defaultAmount: 80 },
    { type: "REASSIGNMENT", label: "改派", defaultAmount: 80 },
  ];

  // 调整类型中英文映射
  const adjustmentTypeMap: Record<string, string> = {
    DESTRUCTION: "销毁",
    RETURN: "退回",
    REASSIGNMENT: "改派",
    COMPENSATION: "赔偿",
  };

  useEffect(() => {
    if (visible) {
      // 只重置表单，不重复设置初始值，避免key冲突
      form.resetFields();
    }
  }, [visible, form]);

  // 自定义验证器：检查是否至少选择了一种调整类型
  const validateAdjustmentTypes = (
    _: unknown,
    value: Array<{ adjustmentType: string; amount: number; enabled: boolean }>
  ) => {
    if (!value || !Array.isArray(value)) {
      return Promise.reject(new Error("请至少选择一种调整类型"));
    }

    const hasEnabled = value.some((item) => item && item.enabled);
    if (!hasEnabled) {
      return Promise.reject(new Error("请至少选择一种调整类型"));
    }

    return Promise.resolve();
  };

  // 处理复选框变化
  const handleCheckboxChange = (
    fieldName: number,
    adjustmentType: { type: string; label: string; defaultAmount: number },
    checked: boolean
  ) => {
    const adjustmentItems = form.getFieldValue("adjustmentItems") || [];

    // 更新当前项的状态和金额
    adjustmentItems[fieldName] = {
      ...adjustmentItems[fieldName],
      adjustmentType: adjustmentType.type,
      enabled: checked,
      amount: checked
        ? adjustmentType.defaultAmount
        : adjustmentItems[fieldName]?.amount || adjustmentType.defaultAmount,
    };

    form.setFieldValue("adjustmentItems", adjustmentItems);

    // 触发表单验证
    form.validateFields(["adjustmentItems"]).catch(() => {
      // 忽略验证错误，只是为了触发错误状态更新
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 验证运单号
      if (!values.trackingNumbers || values.trackingNumbers.trim() === "") {
        message.error("请输入运单号");
        return;
      }

      // 处理运单号（按行分割，去除空行和重复）
      const trackingNumbers = values.trackingNumbers
        .split("\n")
        .map((line: string) => line.trim())
        .filter((line: string) => line.length > 0)
        .filter(
          (value: string, index: number, array: string[]) =>
            array.indexOf(value) === index
        );

      if (trackingNumbers.length === 0) {
        message.error("请输入有效的运单号");
        return;
      }

      if (trackingNumbers.length > 100) {
        message.error("最多支持100个运单号");
        return;
      }

      // 获取启用的调整项
      console.log("表单原始数据:", values.adjustmentItems);

      const enabledAdjustmentItems = values.adjustmentItems
        .map((item, index) => {
          // 确保adjustmentType字段存在，如果不存在则从defaultAdjustmentTypes中获取
          const adjustmentType =
            item.adjustmentType || defaultAdjustmentTypes[index]?.type;
          return {
            ...item,
            adjustmentType,
          };
        })
        .filter((item) => item.enabled)
        .map((item) => ({
          adjustmentType: item.adjustmentType,
          amount: Math.abs(item.amount), // 确保为正数
        }));

      console.log("启用的调整项:", enabledAdjustmentItems);

      setSubmitting(true);

      const requestData: BatchAdjustmentRequest = {
        trackingNumbers,
        adjustmentItems: enabledAdjustmentItems,
        effectiveDate: values.effectiveDate.format("YYYY-MM-DD"),
        description: values.description || "",
        currency: values.currency,
      };

      // 调试输出请求参数
      console.log("批量添加请求参数:", requestData);
      console.log("adjustmentItems详情:", enabledAdjustmentItems);

      // 使用apiClient调用批量创建API
      const response = await apiClient.post<BatchAdjustmentResponse>(
        getApiPath("/financial-adjustments/batch"),
        requestData
      );

      const result = response.data;

      // 添加详细的调试信息
      console.log("API响应完整数据:", result);
      console.log("success:", result.success);
      console.log("data:", result.data);
      console.log("successCount:", result.data?.successCount);
      console.log("failureCount:", result.data?.failureCount);

      // 后端success总是返回true，根据successCount和failureCount判断实际结果
      const { successCount, failureCount, totalCount } = result.data;

      console.log("开始处理结果");
      console.log(
        `successCount: ${successCount}, failureCount: ${failureCount}, totalCount: ${totalCount}`
      );

      // 先关闭批量添加模态框
      console.log("先关闭批量添加模态框");
      handleCancel(); // 立即关闭批量添加模态框

      // 延迟显示结果弹窗，确保批量添加模态框完全关闭
      setTimeout(() => {
        console.log("开始显示结果弹窗");

        // 直接显示详细结果，不需要第一个确认弹窗
        if (failureCount > 0) {
          // 有失败记录，显示详细结果
          showFailureDetails(
            result.data.failureItems,
            successCount,
            failureCount
          );
        } else if (successCount > 0 && failureCount === 0) {
          // 全部成功
          modal.success({
            title: "批量添加成功",
            content: `共 ${successCount} 条记录添加成功`,
          });
        }

        // 执行后续流程
        console.log("执行后续流程：刷新数据");
        onSuccess();
      }, 500);
    } catch (error) {
      console.error("批量添加失败:", error);

      // 尝试提取API返回的错误信息
      let errorMessage = "批量添加失败，请重试";

      if (error && typeof error === "object") {
        // 检查是否有response数据
        if ("response" in error && error.response) {
          const response = error.response as {
            data?: {
              errorMessage?: string;
              message?: string;
            };
            status?: number;
            statusText?: string;
          };
          if (response.data) {
            // 优先使用API返回的errorMessage
            if (response.data.errorMessage) {
              errorMessage = response.data.errorMessage;
            }
            // 如果有详细的错误信息，也显示出来
            else if (response.data.message) {
              errorMessage = response.data.message;
            }
            // 显示HTTP状态码和状态文本
            else if (response.status && response.statusText) {
              errorMessage = `HTTP ${response.status} ${response.statusText}`;
            }
          }
        }
        // 检查是否是网络错误或其他类型的错误
        else if ("message" in error && typeof error.message === "string") {
          errorMessage = error.message;
        }
      }

      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // 显示失败详情的函数
  const showFailureDetails = (
    failureItems: {
      trackingNumber: string;
      adjustmentType: string;
      errorCode: number;
      errorMessage: string;
    }[],
    successCount: number,
    failureCount: number
  ) => {
    console.log("失败详情:", failureItems);

    // 确定弹窗标题
    const modalTitle = successCount > 0 ? "批量添加部分成功" : "批量添加失败";

    // 使用对应的modal方法
    const modalMethod = successCount > 0 ? modal.warning : modal.error;

    modalMethod({
      title: modalTitle,
      width: 700,
      content: (
        <div>
          <div style={{ marginBottom: "16px" }}>
            <strong>结果统计：</strong>
            <div style={{ marginTop: "8px" }}>
              <span style={{ color: "#52c41a", marginRight: "16px" }}>
                ✅ 成功：{successCount} 条
              </span>
              <span style={{ color: "#ff4d4f" }}>
                ❌ 失败：{failureCount} 条
              </span>
            </div>
          </div>

          <div>
            <strong>失败详情：</strong>
            <div
              style={{
                marginTop: "8px",
                padding: "12px",
                background: "#f5f5f5",
                borderRadius: "6px",
                maxHeight: "350px",
                overflowY: "auto",
                border: "1px solid #d9d9d9",
              }}
            >
              {failureItems.map((item, index) => (
                <div
                  key={index}
                  style={{
                    marginBottom: "12px",
                    padding: "8px",
                    background: "#fff",
                    borderRadius: "4px",
                    border: "1px solid #ffccc7",
                    fontSize: "13px",
                  }}
                >
                  <div
                    style={{
                      fontWeight: "bold",
                      color: "#1890ff",
                      marginBottom: "4px",
                    }}
                  >
                    📋 运单号: {item.trackingNumber}
                  </div>
                  <div style={{ marginBottom: "4px" }}>
                    🏷️ 调整类型:{" "}
                    <span style={{ color: "#722ed1" }}>
                      {adjustmentTypeMap[item.adjustmentType] ||
                        item.adjustmentType}
                    </span>
                  </div>
                  <div style={{ color: "#ff4d4f" }}>
                    ❌ 错误信息: {item.errorMessage}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ),
    });
  };

  const handleCancel = () => {
    form.resetFields();
    setSubmitting(false);
    onCancel();
  };

  // 渲染调整类型选择
  const renderAdjustmentTypeSelection = () => {
    return (
      <Form.List name="adjustmentItems">
        {(fields) => (
          <div>
            {fields.map((field, index) => {
              const adjustmentType = defaultAdjustmentTypes[index];
              // 获取当前字段的值
              const currentValues =
                form.getFieldValue(["adjustmentItems", field.name]) || {};
              const currentAmount =
                currentValues.amount !== undefined
                  ? currentValues.amount
                  : adjustmentType.defaultAmount;

              return (
                <div
                  key={`adjustment-${field.key}-${adjustmentType.type}`}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    marginBottom: "12px",
                    padding: "12px",
                    border: "1px solid #d9d9d9",
                    borderRadius: "6px",
                    backgroundColor: "#fafafa",
                  }}
                >
                  <Form.Item
                    {...field}
                    name={[field.name, "enabled"]}
                    valuePropName="checked"
                    style={{ marginBottom: 0, marginRight: "12px" }}
                    key={`enabled-${field.key}-${adjustmentType.type}`}
                  >
                    <input
                      type="checkbox"
                      onChange={(e) =>
                        handleCheckboxChange(
                          field.name,
                          adjustmentType,
                          e.target.checked
                        )
                      }
                    />
                  </Form.Item>

                  <div style={{ minWidth: "60px", fontWeight: "bold" }}>
                    {adjustmentType.label}
                  </div>

                  <div
                    style={{
                      marginLeft: "12px",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <span style={{ marginRight: "8px" }}>金额:</span>
                    <Form.Item
                      {...field}
                      name={[field.name, "amount"]}
                      style={{ marginBottom: 0, marginRight: "8px" }}
                      key={`amount-${field.key}-${adjustmentType.type}`}
                    >
                      <InputNumber
                        min={0}
                        precision={2}
                        style={{ width: "100px" }}
                        placeholder="金额"
                        value={currentAmount}
                        onChange={(value) => {
                          // 更新表单值
                          const adjustmentItems =
                            form.getFieldValue("adjustmentItems") || [];
                          adjustmentItems[field.name] = {
                            ...adjustmentItems[field.name],
                            amount: value || adjustmentType.defaultAmount,
                          };
                          form.setFieldValue(
                            "adjustmentItems",
                            adjustmentItems
                          );
                        }}
                      />
                    </Form.Item>
                    <span>元</span>

                    <Form.Item
                      {...field}
                      name={[field.name, "adjustmentType"]}
                      style={{ display: "none" }}
                      key={`type-${field.key}-${adjustmentType.type}`}
                    >
                      <Input value={adjustmentType.type} />
                    </Form.Item>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </Form.List>
    );
  };

  return (
    <>
      {/* 添加 contextHolder */}
      {contextHolder}
      <Modal
        title="批量添加财务调整"
        open={visible}
        onCancel={handleCancel}
        onOk={handleSubmit}
        confirmLoading={submitting}
        width={650}
        maskClosable={false}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          preserve={false}
          key={visible ? "visible" : "hidden"}
          initialValues={{
            currency: "CNY",
            effectiveDate: dayjs(),
            adjustmentItems: defaultAdjustmentTypes.map((item) => ({
              adjustmentType: item.type,
              amount: item.defaultAmount,
              enabled: false,
            })),
          }}
        >
          <Form.Item
            name="trackingNumbers"
            label="运单号列表"
            rules={[{ required: true, message: "请输入运单号" }]}
            extra="每行输入一个运单号，最多支持100个"
          >
            <Input.TextArea
              rows={6}
              placeholder="请输入运单号，每行一个&#10;例如：&#10;ZM2024010001&#10;ZM2024010002&#10;ZM2024010003"
              showCount
              maxLength={5000}
            />
          </Form.Item>

          <Form.Item
            name="adjustmentItems"
            label="调整类型和金额"
            required
            rules={[{ validator: validateAdjustmentTypes }]}
          >
            {renderAdjustmentTypeSelection()}
          </Form.Item>

          <Form.Item
            name="effectiveDate"
            label="生效日期"
            rules={[{ required: true, message: "请选择生效日期" }]}
          >
            <DatePicker style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item
            name="currency"
            label="货币类型"
            rules={[{ required: true, message: "请选择货币类型" }]}
          >
            <Select>
              <Select.Option value="CNY">人民币 (CNY)</Select.Option>
              <Select.Option value="USD">美元 (USD)</Select.Option>
              <Select.Option value="EUR">欧元 (EUR)</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="description" label="调整说明">
            <Input.TextArea
              rows={3}
              placeholder="请输入调整说明（可选）"
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
