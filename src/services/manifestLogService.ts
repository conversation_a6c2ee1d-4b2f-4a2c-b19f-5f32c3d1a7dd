import { apiClient, ApiResponse } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 操作类型枚举
export enum OperationType {
  UPDATE_TEMPLATE_TYPE = "UPDATE_TEMPLATE_TYPE", // 修改模板类型
  APPROVE = "APPROVE", // 审核通过
  REJECT = "REJECT", // 审核拒绝
  PICKUP = "PICKUP", // 揽件
  SHIP = "SHIP", // 发货
  DELIVER = "DELIVER", // 送达
  ADJUST_FEE = "ADJUST_FEE", // 调整费用
  MARK_EXCEPTION = "MARK_EXCEPTION", // 标记异常
  CREATE = "CREATE", // 创建运单
  UPDATE = "UPDATE", // 更新运单
  DELETE = "DELETE", // 删除运单
}

// 操作日志详情接口
export interface ChangeDetails {
  oldTemplateType?: number;
  oldTemplateTypeName?: string;
  newTemplateType?: number;
  newTemplateTypeName?: string;
  oldCost?: number;
  newCost?: number;
  costDifference?: number;
  oldStatus?: number;
  newStatus?: number;
  statusName?: string;
  [key: string]: string | number | boolean | undefined; // 支持其他字段
}

// 运单操作日志接口
export interface ManifestOperationLog {
  id: number;
  manifestId: number;
  operatorId: number;
  operatorName: string;
  operationTime: string;
  operationType: OperationType;
  description: string;
  changeDetails?: ChangeDetails;
  context: string; // WEB, MOBILE, API等
  ipAddress: string;
}

// 查询参数接口
export interface ManifestLogQueryParams {
  page?: number;
  pageSize?: number;
}

// 查询结果接口
export interface ManifestLogQueryResult {
  total: number;
  list: ManifestOperationLog[];
}

/**
 * 根据运单ID查询操作日志
 * @param manifestId 运单ID
 * @param params 查询参数
 * @returns 操作日志列表
 */
export const getManifestOperationLogs = async (
  manifestId: number,
  params: ManifestLogQueryParams = {}
): Promise<ManifestLogQueryResult> => {
  try {
    const response = await apiClient.get<ApiResponse<ManifestLogQueryResult>>(
      getApiPath(`/manifests/${manifestId}/operation-logs`),
      { params }
    );
    return response.data.data || { total: 0, list: [] };
  } catch (error) {
    console.error("获取运单操作日志失败:", error);
    // 返回模拟数据用于开发测试
    return {
      total: 3,
      list: [
        {
          id: 1,
          manifestId,
          operatorId: 1001,
          operatorName: "张三",
          operationTime: "2024-01-15 14:30:25",
          operationType: OperationType.UPDATE_TEMPLATE_TYPE,
          description:
            "修改运单模板类型：东京-普通货物 → 东京-带电货物，运费从 25.50 调整为 35.00（差额：9.50）",
          changeDetails: {
            oldTemplateType: 1,
            oldTemplateTypeName: "东京-普通货物",
            newTemplateType: 2,
            newTemplateTypeName: "东京-带电货物",
            oldCost: 25.5,
            newCost: 35.0,
            costDifference: 9.5,
          },
          context: "WEB",
          ipAddress: "*************",
        },
        {
          id: 2,
          manifestId,
          operatorId: 1002,
          operatorName: "李四",
          operationTime: "2024-01-15 10:15:30",
          operationType: OperationType.APPROVE,
          description: "审核通过运单",
          changeDetails: {
            oldStatus: 0,
            newStatus: 1,
            statusName: "已审核",
          },
          context: "WEB",
          ipAddress: "*************",
        },
        {
          id: 3,
          manifestId,
          operatorId: 1003,
          operatorName: "王五",
          operationTime: "2024-01-14 16:45:12",
          operationType: OperationType.CREATE,
          description: "创建运单",
          context: "WEB",
          ipAddress: "*************",
        },
      ],
    };
  }
};

// 操作类型中文映射
export const OPERATION_TYPE_MAP: Record<OperationType, string> = {
  [OperationType.UPDATE_TEMPLATE_TYPE]: "修改模板类型",
  [OperationType.APPROVE]: "审核通过",
  [OperationType.REJECT]: "审核拒绝",
  [OperationType.PICKUP]: "揽件",
  [OperationType.SHIP]: "发货",
  [OperationType.DELIVER]: "送达",
  [OperationType.ADJUST_FEE]: "调整费用",
  [OperationType.MARK_EXCEPTION]: "标记异常",
  [OperationType.CREATE]: "创建运单",
  [OperationType.UPDATE]: "更新运单",
  [OperationType.DELETE]: "删除运单",
};
