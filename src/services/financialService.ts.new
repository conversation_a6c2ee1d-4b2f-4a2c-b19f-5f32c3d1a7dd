import { apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 费用调整类型接口
export interface FinancialAdjustmentType {
  value: string;
  label: string;
  description?: string;
}

// 费用调整类型项接口
interface AdjustmentTypeItem {
  id: number;
  name: string;
  description: string;
}

// 费用调整类型分页响应数据接口
interface FinancialAdjustmentTypesData {
  total: number;
  list: AdjustmentTypeItem[];
}

// 费用调整类型分页响应接口
interface FinancialAdjustmentTypesResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: FinancialAdjustmentTypesData;
}

// 费用调整类型选项（初始化为空数组，将从后端获取）
export let ADJUSTMENT_TYPE_OPTIONS: FinancialAdjustmentType[] = [];

/**
 * 获取费用调整类型列表
 * @param keyword 搜索关键词
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 费用调整类型列表
 */
export const fetchAdjustmentTypes = async (
  keyword: string = '', 
  page: number = 1, 
  pageSize: number = 20
): Promise<FinancialAdjustmentType[]> => {
  try {
    // 调用API获取费用调整类型
    const response = await apiClient.get<FinancialAdjustmentTypesResponse>(
      getApiPath('/financial-adjustment-types'),
      { params: { keyword, page, pageSize } }
    );
    
    // 验证响应数据格式
    if (!response.data?.data?.list) {
      throw new Error('Invalid response format: missing required fields');
    }
    
    // 转换数据格式为下拉框需要的格式
    const formattedItems: FinancialAdjustmentType[] = response.data.data.list.map((item) => ({
      value: item.id.toString(),
      label: item.name,
      description: item.description
    }));
    
    // 更新全局的调整类型选项
    ADJUSTMENT_TYPE_OPTIONS = formattedItems;
    
    return formattedItems;
  } catch (error) {
    console.error('Failed to fetch adjustment types:', error);
    return [];
  }
};

// 货币单位选项
export const CURRENCY_OPTIONS = [
  { value: "CNY", label: "人民币 (CNY)" },
  { value: "USD", label: "美元 (USD)" },
  { value: "EUR", label: "欧元 (EUR)" },
  { value: "GBP", label: "英镑 (GBP)" },
  { value: "JPY", label: "日元 (JPY)" },
];

// 费用调整请求体接口
interface AddFinancialAdjustmentRequest {
  manifestId: number; // 运单ID
  adjustmentType: string; // 调整类型
  description?: string; // 调整说明
  amount: number; // 调整金额
  currency: string; // 货币单位
  effectiveDate: string; // 生效日期
  customerAccountId: number; // 客户账户ID
  attachmentPaths?: string; // 附件路径，多个用逗号分隔
}

// 费用调整响应接口
interface AddFinancialAdjustmentResponse {
  id: number; // 调整记录ID
}

// 作废费用调整请求体接口
interface VoidFinancialAdjustmentRequest {
  id: number; // 调整记录ID
  voidReason: string; // 作废原因
}

// 作废费用调整响应接口
interface VoidFinancialAdjustmentResponse {
  success: boolean; // 操作是否成功
}

// 创建费用调整类型请求接口
export interface CreateAdjustmentTypeRequest {
  name: string;
  description?: string;
}

// 创建费用调整类型响应接口
export interface CreateAdjustmentTypeResponse {
  id: number;
  name: string;
  description: string;
}

/**
 * 添加费用调整
 * @param data 费用调整数据
 * @returns 添加结果，包含新创建的调整记录ID
 * @throws {Error} 当请求失败时抛出错误
 */
export const addFinancialAdjustment = async (
  data: AddFinancialAdjustmentRequest
): Promise<AddFinancialAdjustmentResponse> => {
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: AddFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments"), data);
  
  if (!response.data || !response.data.data) {
    throw new Error('Failed to add financial adjustment: Invalid response from server');
  }
  
  return response.data.data;
};

/**
 * 作废费用调整
 * @param data 作废请求数据，包含调整记录ID和作废原因
 * @returns 作废操作结果
 * @throws {Error} 当请求失败时抛出错误
 */
export const voidFinancialAdjustment = async (
  data: VoidFinancialAdjustmentRequest
): Promise<VoidFinancialAdjustmentResponse> => {
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: VoidFinancialAdjustmentResponse;
  }>(getApiPath("/financial-adjustments/void"), data);
  
  if (!response.data || !response.data.data) {
    throw new Error('Failed to void financial adjustment: Invalid response from server');
  }
  
  return response.data.data;
};

/**
 * 创建费用调整类型
 * @param data 创建费用调整类型请求数据
 * @returns 创建的费用调整类型
 * @throws {Error} 当请求失败时抛出错误
 */
export const createAdjustmentType = async (
  data: CreateAdjustmentTypeRequest
): Promise<CreateAdjustmentTypeResponse> => {
  const response = await apiClient.post<{
    success: boolean;
    errorCode: number;
    errorMessage: string;
    requestId: string;
    timestamp: string;
    data: CreateAdjustmentTypeResponse;
  }>(getApiPath("/financial-adjustment-types"), data);
  
  if (!response.data || !response.data.data) {
    throw new Error('Failed to create adjustment type: Invalid response from server');
  }
  
  // 更新全局的调整类型选项
  const newType = {
    value: response.data.data.id.toString(),
    label: response.data.data.name,
    description: response.data.data.description || ''
  };
  
  // 添加到全局选项
  ADJUSTMENT_TYPE_OPTIONS = [...ADJUSTMENT_TYPE_OPTIONS, newType];
  
  return response.data.data;
};
