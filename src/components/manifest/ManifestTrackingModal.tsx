import React, { useEffect, useState } from "react";
import { Modal, Timeline, Spin, Alert, Empty, Typography } from "antd";
import {
  getManifestTrackings,
  TrackingEvent,
} from "../../services/problemTicketService";

const { Title } = Typography;

// 定义错误对象的通用接口
interface ApiError {
  message: string;
  response?: {
    status?: number;
    statusText?: string;
    data?: {
      errorMessage?: string;
      errorCode?: number;
    };
  };
}

interface ManifestTrackingModalProps {
  visible: boolean;
  manifestId: number | null;
  expressNumber?: string; // 用于显示在标题中
  onClose: () => void;
}

/**
 * 运单轨迹查看Modal组件
 */
const ManifestTrackingModal: React.FC<ManifestTrackingModalProps> = ({
  visible,
  manifestId,
  expressNumber,
  onClose,
}) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [trackings, setTrackings] = useState<TrackingEvent[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 加载轨迹数据
  useEffect(() => {
    if (visible && manifestId !== null) {
      setLoading(true);
      setError(null);
      setTrackings([]);

      getManifestTrackings(manifestId)
        .then((trackingData: TrackingEvent[]) => {
          setTrackings(trackingData);
        })
        .catch((err: unknown) => {
          console.error("获取物流轨迹失败 - 原始错误对象:", err);
          const apiError = err as ApiError;
          let displayMessage = "获取物流轨迹失败，请稍后重试。";
          const statusCode = apiError.response?.status;
          const backendErrorMessage = apiError.response?.data?.errorMessage;
          const genericMessage = apiError.message;

          if (statusCode && statusCode >= 500 && statusCode <= 599) {
            displayMessage =
              backendErrorMessage ||
              `获取物流轨迹时发生服务器错误 (状态码: ${statusCode})。`;
          } else if (backendErrorMessage) {
            displayMessage = backendErrorMessage;
          } else if (genericMessage) {
            displayMessage = genericMessage;
          }
          setError(displayMessage);
        })
        .finally(() => {
          setLoading(false);
        });
    } else if (!visible) {
      // 重置状态
      setTrackings([]);
      setError(null);
      setLoading(false);
    }
  }, [visible, manifestId]);

  return (
    <Modal
      title={
        <div>
          <Title level={4} style={{ margin: 0 }}>
            物流轨迹
          </Title>
          {expressNumber && (
            <div style={{ fontSize: "14px", color: "#666", marginTop: "4px" }}>
              运单号：{expressNumber}
            </div>
          )}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={700}
      destroyOnClose
      maskClosable={true}
    >
      {loading && (
        <div style={{ textAlign: "center", padding: "40px 20px" }}>
          <Spin size="large" tip="加载物流轨迹中..." />
        </div>
      )}

      {error && !loading && (
        <Alert
          message="加载物流轨迹错误"
          description={error}
          type="error"
          showIcon
          closable
          onClose={() => setError(null)}
          style={{ marginBottom: 16 }}
        />
      )}

      {!loading && !error && trackings.length === 0 && (
        <Empty description="暂无物流轨迹信息" style={{ margin: "40px 0" }} />
      )}

      {!loading && !error && trackings.length > 0 && (
        <div
          style={{ maxHeight: "60vh", overflowY: "auto", padding: "16px 0" }}
        >
          <Timeline>
            {trackings.map((event) => (
              <Timeline.Item
                key={event.id}
                color={
                  event.status === -1
                    ? "red"
                    : event.status >= 8
                    ? "green"
                    : "blue"
                }
              >
                <div>
                  <div style={{ fontWeight: "bold", marginBottom: "4px" }}>
                    {event.track}
                  </div>
                  <div style={{ fontSize: "12px", color: "#888" }}>
                    时间: {event.time || event.createTime}
                    {event.place && ` | 地点: ${event.place}`}
                  </div>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        </div>
      )}
    </Modal>
  );
};

export default ManifestTrackingModal;
