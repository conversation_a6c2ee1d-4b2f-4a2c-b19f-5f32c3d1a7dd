import React from "react";
import { Modal, Button, Typography, Space } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";

const { Text, Title } = Typography;

interface SessionExpiredModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSessionExpiredLogout: () => void;
}

const SessionExpiredModal: React.FC<SessionExpiredModalProps> = ({
  isOpen,
  onClose,
  onSessionExpiredLogout,
}) => {
  const handleLogin = () => {
    // 调用父组件提供的会话过期登出回调
    onSessionExpiredLogout();
  };

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: "#faad14" }} />
          <Title level={5} style={{ margin: 0 }}>
            会话已过期
          </Title>
        </Space>
      }
      open={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="back" onClick={onClose}>
          关闭
        </Button>,
        <Button key="login" type="primary" onClick={handleLogin}>
          重新登录
        </Button>,
      ]}
      maskClosable={false}
      closable={true}
      centered
    >
      <div style={{ padding: "20px 0" }}>
        <Text>您的登录会话已过期，请重新登录以继续使用系统。</Text>
      </div>
    </Modal>
  );
};

export default SessionExpiredModal;
