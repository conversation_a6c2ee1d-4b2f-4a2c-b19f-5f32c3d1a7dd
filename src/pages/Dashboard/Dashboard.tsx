import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  Row,
  Col,
  Card,
  Statistic,
  Button,
  List,
  Typography,
  Tag,
  Space,
  Progress,
  Avatar,
  message,
  Form,
  Select,
  DatePicker,
  Table,
  Empty,
  Spin,
  Divider,
  Radio,
} from "antd";
import type { SortOrder } from "antd/es/table/interface";
import {
  ArrowUpOutlined,
  PlusOutlined,
  SearchOutlined,
  FileTextOutlined,
  UserOutlined,
  AuditOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
  DollarOutlined,
} from "@ant-design/icons";
import styles from "./Dashboard.module.css";
import { useNavigate } from "react-router-dom";
import { useSharedPendingCount } from "../../contexts/PendingCountContext";
import { 
  getProblemManifestCount, 
  getMonthlyShipmentCount, 
  getTodayShipmentCount,
  ShipmentCountData 
} from "../../services/problemTicketService";
import { 
  queryStatistics, 
  QueryStatisticsParams, 
  QueryStatisticsResponse,
  StatisticItem 
} from "../../services/statisticsService";
import { fetchUserOptions, UserOption } from "../../services/userService";
import { ApiError } from "../../services/apiClient";
import { debounce } from "../../utils/debounce";
import dayjs from "dayjs";
// 引入图表库（如果需要真实图表）或使用占位符
// import { Pie, Line } from '@ant-design/charts'; // 示例

const { Title, Text, Paragraph } = Typography;

// 为模拟数据添加类型
interface TodoItem {
  id: string;
  title: string;
  status: string;
  priority: "high" | "medium" | "low";
}

interface ActivityItem {
  id: string;
  user: string;
  action: string;
  time: string;
}

// 模拟待办事项数据
const todoData: TodoItem[] = [
  {
    id: "1",
    title: "订单 #ORD12345 待审核",
    status: "pending",
    priority: "high",
  },
  {
    id: "2",
    title: "运单 #SHP67890 需要补充清关文件",
    status: "action-required",
    priority: "medium",
  },
  {
    id: "3",
    title: "货物 #WHS001 即将超过免费仓储期 (3天)",
    status: "warning",
    priority: "low",
  },
  {
    id: "4",
    title: "客户 [ABC公司] 的合同即将到期",
    status: "info",
    priority: "medium",
  },
];

// 模拟最新动态数据
const activityData: ActivityItem[] = [
  {
    id: "act1",
    user: "管理员",
    action: "创建了新订单 #ORD54321",
    time: "5分钟前",
  },
  {
    id: "act2",
    user: "操作员A",
    action: '更新了运单 #SHP98765 的状态为"运输中"',
    time: "1小时前",
  },
  {
    id: "act3",
    user: "系统",
    action: "检测到运单 #SHP11223 可能延误",
    time: "3小时前",
  },
  { id: "act4", user: "财务B", action: "生成了结算单 #BILL0088", time: "昨天" },
];

// 模拟图表数据 (如果使用真实图表库，数据结构可能不同)
// const pieChartData = [...] // 保持不变
// const lineChartData = [...] // 保持不变

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { pendingCount, loading: pendingCountLoading } =
    useSharedPendingCount();

  // 新增状态：问题订单数量及其加载状态
  const [problemOrderCount, setProblemOrderCount] = useState<number | null>(
    null
  );
  const [problemOrderLoading, setProblemOrderLoading] = useState<boolean>(true);

  // 新增状态：发货量统计数据
  const [monthlyShipmentData, setMonthlyShipmentData] = useState<ShipmentCountData | null>(null);
  const [todayShipmentData, setTodayShipmentData] = useState<ShipmentCountData | null>(null);
  const [shipmentLoading, setShipmentLoading] = useState<boolean>(true);

  // 发货量查询相关状态
  const [queryForm] = Form.useForm();
  const [queryData, setQueryData] = useState<QueryStatisticsResponse | null>(null);
  const [queryLoading, setQueryLoading] = useState<boolean>(false);
  const [queryParams, setQueryParams] = useState<QueryStatisticsParams>({
    queryType: 'day',
    year: dayjs().year(),
    month: dayjs().month() + 1,
    day: dayjs().date(),
    sortBy: 'shipment',
    sortOrder: 'desc'
  });

  // 用户选项数据
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [userLoading, setUserLoading] = useState<boolean>(false);
  
  // 图表切换状态
  const [activeChart, setActiveChart] = useState<'shipment' | 'fee'>('shipment');
  const [userPage, setUserPage] = useState<number>(1);
  const [userTotal, setUserTotal] = useState<number>(0);
  const [userSearchKeyword, setUserSearchKeyword] = useState<string>("");

  useEffect(() => {
    const fetchProblemOrderCount = async () => {
      try {
        setProblemOrderLoading(true);
        const count = await getProblemManifestCount();
        setProblemOrderCount(count);
      } catch (error) {
        console.error("获取问题订单数量失败:", error);
        if (error instanceof ApiError) {
          message.error(error.message || "获取问题订单数量失败 (API Error)");
        } else if (error instanceof Error) {
          message.error(error.message || "获取问题订单数量失败 (Error)");
        } else {
          message.error("获取问题订单数量失败，请稍后重试 (Unknown Error)");
        }
        setProblemOrderCount(null); // 出错时可以设置为 null 或 0
      } finally {
        setProblemOrderLoading(false);
      }
    };

    const fetchShipmentCounts = async () => {
      try {
        setShipmentLoading(true);
        const [monthlyData, todayData] = await Promise.all([
          getMonthlyShipmentCount(),
          getTodayShipmentCount()
        ]);
        setMonthlyShipmentData(monthlyData);
        setTodayShipmentData(todayData);
      } catch (error) {
        console.error("获取发货量统计失败:", error);
        if (error instanceof ApiError) {
          message.error(error.message || "获取发货量统计失败 (API Error)");
        } else if (error instanceof Error) {
          message.error(error.message || "获取发货量统计失败 (Error)");
        } else {
          message.error("获取发货量统计失败，请稍后重试 (Unknown Error)");
        }
        setMonthlyShipmentData(null);
        setTodayShipmentData(null);
      } finally {
        setShipmentLoading(false);
      }
    };

    fetchProblemOrderCount();
    fetchShipmentCounts();
    loadUserOptions("", 1);
  }, []);

  // 加载用户选项数据 (参考ManifestListPage模式)
  const loadUserOptions = useCallback(
    async (keyword: string = "", page: number = 1, append: boolean = false) => {
      try {
        setUserLoading(true);
        const apiResponse = await fetchUserOptions({
          page,
          pageSize: 20,
          keyword: keyword.trim(),
        });

        if (apiResponse.success && apiResponse.data) {
          const { list, total } = apiResponse.data;
          setUserOptions((prevOptions) =>
            append ? [...prevOptions, ...list] : list
          );
          setUserTotal(total);
          setUserPage(page);
        } else {
          console.error(
            "Failed to load user options: Data structure inside ApiResponse.data is invalid.",
            apiResponse
          );
          setUserOptions([]);
          setUserTotal(0);
        }
      } catch (error) {
        console.error(
          "Failed to load user options:",
          error instanceof Error ? error.message : String(error)
        );
        setUserOptions([]);
        setUserTotal(0);
      } finally {
        setUserLoading(false);
      }
    },
    []
  );

  // 防抖用户搜索
  const debouncedUserSearch = useCallback(
    debounce((keyword: string) => {
      console.log(
        "[Dashboard] debouncedUserSearch triggered with keyword:",
        keyword
      );
      setUserOptions([]);
      setUserPage(1);
      setUserSearchKeyword(keyword);
      loadUserOptions(keyword, 1, false);
    }, 300),
    [loadUserOptions]
  );

  // 处理用户下拉框滚动加载
  const handleUserPopupScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { target } = event;
    const { scrollTop, scrollHeight, clientHeight } = target as HTMLElement;
    if (scrollTop + clientHeight === scrollHeight) {
      if (userOptions.length < userTotal && !userLoading) {
        loadUserOptions(userSearchKeyword, userPage + 1, true);
      }
    }
  };

  // 查询统计数据
  const fetchQueryData = async (params?: QueryStatisticsParams) => {
    const searchParams = params || queryParams;
    try {
      setQueryLoading(true);
      const data = await queryStatistics(searchParams);
      setQueryData(data);
    } catch (error) {
      console.error("查询统计数据失败:", error);
      if (error instanceof Error) {
        message.error(error.message || "查询统计数据失败");
      } else {
        message.error("查询统计数据失败，请稍后重试");
      }
      setQueryData(null);
    } finally {
      setQueryLoading(false);
    }
  };

  // 处理查询按钮点击
  const handleSearch = () => {
    const formValues = queryForm.getFieldsValue();
    const searchParams: QueryStatisticsParams = {
      queryType: formValues.queryType || 'all',
      year: formValues.year ? dayjs(formValues.year).year() : undefined,
      month: formValues.month ? dayjs(formValues.month).month() + 1 : undefined,
      day: formValues.day ? dayjs(formValues.day).date() : undefined,
      userId: formValues.userId,
      sortBy: queryParams.sortBy,
      sortOrder: queryParams.sortOrder
    };
    setQueryParams(searchParams);
    fetchQueryData(searchParams);
  };

  // 处理表格排序
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    let sortBy: 'shipment' | 'fee' = 'shipment';
    let sortOrder: 'asc' | 'desc' = 'desc';
    
    if (sorter && sorter.field) {
      sortBy = sorter.field === 'shipmentCount' ? 'shipment' : 'fee';
      sortOrder = sorter.order === 'ascend' ? 'asc' : 'desc';
      
      const newParams = { ...queryParams, sortBy, sortOrder };
      setQueryParams(newParams);
      fetchQueryData(newParams);
    }
  };

  const handleNavigateToAudit = () => {
    navigate("/forecast/audit");
  };

  const handleNavigateToProblemTickets = () => {
    navigate("/orders/problem-tickets"); // 导航到问题订单页面
  };

  // 定义为函数式组件
  // 简单的饼图/环形图占位符
  const PieChartPlaceholder = () => (
    <div className={styles.chartPlaceholder}>
      <Title level={5} style={{ textAlign: "center", marginBottom: "20px" }}>
        运单状态分布
      </Title>
      <Progress
        type="circle"
        percent={45}
        format={() => "运输中"}
        style={{ marginRight: 15 }}
      />
      <Progress
        type="circle"
        percent={18}
        format={() => "清关中"}
        status="exception"
        style={{ marginRight: 15 }}
      />
      <Progress
        type="circle"
        percent={80}
        format={() => "已签收"}
        status="success"
      />
      {/* 可以用更复杂的 SVG 或 Canvas 实现 */}
      <Paragraph
        type="secondary"
        style={{ marginTop: 20, textAlign: "center" }}
      >
        此处为图表示例
      </Paragraph>
    </div>
  );

  // 发货量图表组件
  const ShipmentChart = ({ data }: { data: StatisticItem[] }) => {
    const topUsers = data.slice(0, 5); // 显示前5名用户，与运费图表保持一致
    const maxShipment = topUsers.length > 0 ? Math.max(...topUsers.map(d => d.shipmentCount)) : 0;
    
    // 颜色数组，与运费图表保持一致
    const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
    
    return (
      <div style={{ padding: '16px' }}>
        <Row gutter={[8, 12]}>
          {topUsers.map((item, index) => {
            const percentage = maxShipment > 0 ? (item.shipmentCount / maxShipment) * 100 : 0;
            
            return (
              <Col span={24} key={item.id}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'space-between',
                  padding: '8px 0'
                }}>
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    flex: 1, 
                    minWidth: 0 
                  }}>
                    <div style={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: colors[index],
                      marginRight: 8,
                      flexShrink: 0
                    }} />
                    <span style={{ 
                      fontSize: '12px', 
                      color: '#666',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {item.userNickname}
                    </span>
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    fontWeight: 500,
                    color: '#262626',
                    marginLeft: 8
                  }}>
                    {item.shipmentCount}单
                  </div>
                </div>
                <Progress
                  percent={Math.round(percentage * 100) / 100}
                  strokeColor={colors[index]}
                  trailColor="#f5f5f5"
                  strokeWidth={6}
                  showInfo={false}
                />
              </Col>
            );
          })}
        </Row>
        {topUsers.length === 0 && (
          <Empty 
            image={Empty.PRESENTED_IMAGE_SIMPLE} 
            description="暂无数据" 
            style={{ margin: '20px 0' }}
          />
        )}
      </div>
    );
  };

  // 运费饼图组件
  const FeeChart = ({ data }: { data: StatisticItem[] }) => {
    const topUsers = data.slice(0, 5); // 显示前5名用户
    const totalFee = data.reduce((sum, item) => sum + item.totalFee, 0);
    
    // 颜色数组，更好看的颜色搭配
    const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'];
    
    return (
      <div style={{ padding: '16px' }}>
        <Row gutter={[8, 12]}>
          {topUsers.map((item, index) => {
            const percentage = totalFee > 0 ? ((item.totalFee / totalFee) * 100) : 0;
            
            return (
              <Col span={24} key={item.id}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'space-between',
                  padding: '8px 0'
                }}>
                  <div style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    flex: 1, 
                    minWidth: 0 
                  }}>
                    <div style={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      backgroundColor: colors[index],
                      marginRight: 8,
                      flexShrink: 0
                    }} />
                    <span style={{ 
                      fontSize: '12px', 
                      color: '#666',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {item.userNickname}
                    </span>
                  </div>
                  <div style={{ 
                    fontSize: '12px', 
                    fontWeight: 500,
                    color: '#262626',
                    marginLeft: 8
                  }}>
                    ¥{item.totalFee.toLocaleString()}
                  </div>
                </div>
                <Progress
                  percent={Math.round(percentage * 100) / 100}
                  strokeColor={colors[index]}
                  trailColor="#f5f5f5"
                  strokeWidth={6}
                  showInfo={false}
                />
              </Col>
            );
          })}
        </Row>
        {topUsers.length === 0 && (
          <Empty 
            image={Empty.PRESENTED_IMAGE_SIMPLE} 
            description="暂无数据" 
            style={{ margin: '20px 0' }}
          />
        )}
      </div>
    );
  };

  // 简单的折线图占位符
  const LineChartPlaceholder = () => (
    <div className={styles.chartPlaceholder}>
      <Title level={5} style={{ textAlign: "center", marginBottom: "20px" }}>
        近期订单量趋势
      </Title>
      {/* 绘制简单的折线 */}
      <svg
        width="100%"
        height="150"
        viewBox="0 0 300 150"
        preserveAspectRatio="none"
      >
        <polyline
          points="0,130 50,100 100,110 150,80 200,70 250,90 300,50"
          fill="none"
          stroke="#1890ff"
          strokeWidth="2"
        />
        <line
          x1="0"
          y1="140"
          x2="300"
          y2="140"
          stroke="#f0f0f0"
          strokeWidth="1"
        />
        <line x1="0" y1="0" x2="0" y2="140" stroke="#f0f0f0" strokeWidth="1" />
      </svg>
      <Paragraph
        type="secondary"
        style={{ marginTop: 10, textAlign: "center" }}
      >
        此处为图表示例
      </Paragraph>
    </div>
  );

  // 表格列定义
  const tableColumns = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      title: '用户',
      dataIndex: 'userNickname',
      key: 'userNickname',
    },
    {
      title: '发货量',
      dataIndex: 'shipmentCount',
      key: 'shipmentCount',
      render: (count: number) => (
        <span>
          <BarChartOutlined style={{ marginRight: 4, color: '#1890ff' }} />
          {count} 单
        </span>
      ),
      sorter: true,
      sortOrder: queryParams.sortBy === 'shipment' ? (queryParams.sortOrder === 'asc' ? 'ascend' : 'descend') as SortOrder : undefined,
    },
    {
      title: '运费总额',
      dataIndex: 'totalFee',
      key: 'totalFee',
      render: (fee: number) => (
        <span>
          <DollarOutlined style={{ marginRight: 4, color: '#52c41a' }} />
          ¥{fee.toLocaleString()}
        </span>
      ),
      sorter: true,
      sortOrder: queryParams.sortBy === 'fee' ? (queryParams.sortOrder === 'asc' ? 'ascend' : 'descend') as SortOrder : undefined,
    },
    {
      title: '统计日期',
      dataIndex: 'statisticDate',
      key: 'statisticDate',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
  ];

  return (
    <div className={styles.dashboardContainer}>
      {/* 1. 概览统计卡片 */}
      <Row gutter={[16, 16]} className={styles.summaryRow}>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card bordered={false}>
            <Statistic
              title="今日发货量"
              value={shipmentLoading ? "-" : todayShipmentData?.count || 0}
              precision={0}
              valueStyle={{ color: "#1890ff" }}
              prefix={<ArrowUpOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {todayShipmentData?.date ? `统计日期: ${todayShipmentData.date}` : "统计中..."}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card bordered={false}>
            <Statistic
              title="本月发货量"
              value={shipmentLoading ? "-" : monthlyShipmentData?.count || 0}
              precision={0}
              valueStyle={{ color: "#52c41a" }}
              prefix={<ArrowUpOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {monthlyShipmentData?.date ? `统计月份: ${monthlyShipmentData.date}` : "统计中..."}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card bordered={false}>
            <Statistic
              title="今日新增订单"
              value={112}
              precision={0}
              valueStyle={{ color: "#faad14" }}
              prefix={<ArrowUpOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              较昨日 +12%
            </Text>
          </Card>
        </Col>
      </Row>

      {/* 第二行：业务统计卡片 */}
      <Row gutter={[16, 16]} className={styles.summaryRow}>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            bordered={false}
            hoverable
            onClick={handleNavigateToAudit}
            className={styles.clickableCard}
          >
            <Statistic
              title="待审核预报"
              value={pendingCountLoading ? "-" : pendingCount}
              valueStyle={{ color: pendingCount > 0 ? "#faad14" : undefined }}
              prefix={<AuditOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {pendingCount > 0 ? "点击处理" : "暂无待审核"}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card
            bordered={false}
            hoverable
            onClick={handleNavigateToProblemTickets}
            className={styles.clickableCard}
          >
            <Statistic
              title="问题订单"
              value={
                problemOrderLoading
                  ? "-"
                  : problemOrderCount !== null
                  ? problemOrderCount
                  : "N/A"
              }
              valueStyle={{
                color:
                  problemOrderCount && problemOrderCount > 0
                    ? "#cf1322"
                    : undefined,
              }}
              prefix={<ExclamationCircleOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              {problemOrderCount && problemOrderCount > 0
                ? "点击查看并处理"
                : "暂无问题订单"}
            </Text>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card bordered={false}>
            <Statistic
              title="待处理运单"
              value={23}
              valueStyle={{ color: "#cf1322" }}
              prefix={<ExclamationCircleOutlined />}
              suffix="单"
            />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              需尽快处理
            </Text>
          </Card>
        </Col>
        {/* <Col xs={24} sm={12} md={12} lg={6}>
          <Card bordered={false}>
            <Statistic title="在途总数" value={1289} suffix="单" />
            <Text type="secondary" style={{ fontSize: "12px" }}>
              全球运输中
            </Text>
          </Card>
        </Col> */}
      </Row>

      {/* 发货量查询区域 */}
      <Row gutter={[16, 16]} style={{ marginTop: '16px', marginBottom: '24px' }}>
        <Col span={24}>
          <Card title="发货量统计查询" bordered={false}>
            {/* 查询条件 */}
            <Form
              form={queryForm}
              layout="inline"
              style={{ marginBottom: '16px' }}
              initialValues={{ 
                queryType: 'day',
                year: dayjs(),
                month: dayjs(),
                day: dayjs()
              }}
            >
              <Form.Item name="queryType" label="查询类型" style={{ marginBottom: '8px' }}>
                <Select 
                  style={{ width: 120 }}
                  onChange={(value) => {
                    // 根据查询类型设置默认日期
                    if (value === 'year') {
                      queryForm.setFieldsValue({ 
                        year: dayjs(),
                        month: undefined,
                        day: undefined 
                      });
                    } else if (value === 'month') {
                      queryForm.setFieldsValue({ 
                        year: dayjs(),
                        month: dayjs(),
                        day: undefined 
                      });
                    } else if (value === 'day') {
                      queryForm.setFieldsValue({ 
                        year: dayjs(),
                        month: dayjs(),
                        day: dayjs()
                      });
                    }
                  }}
                >
                  <Select.Option value="all">全部</Select.Option>
                  <Select.Option value="year">按年</Select.Option>
                  <Select.Option value="month">按月</Select.Option>
                  <Select.Option value="day">按日</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item noStyle shouldUpdate>
                {({ getFieldValue }) => {
                  const queryType = getFieldValue('queryType');
                  return queryType !== 'all' ? (
                    <Form.Item name="year" label="年份" style={{ marginBottom: '8px' }}>
                      <DatePicker
                        picker="year"
                        placeholder="选择年份"
                        style={{ width: 120 }}
                        format="YYYY"
                        allowClear={false}
                      />
                    </Form.Item>
                  ) : null;
                }}
              </Form.Item>

              <Form.Item noStyle shouldUpdate>
                {({ getFieldValue }) => {
                  const queryType = getFieldValue('queryType');
                  return (queryType === 'month' || queryType === 'day') ? (
                    <Form.Item name="month" label="月份" style={{ marginBottom: '8px' }}>
                      <DatePicker
                        picker="month"
                        placeholder="选择月份"
                        style={{ width: 120 }}
                        format="YYYY-MM"
                        allowClear={false}
                      />
                    </Form.Item>
                  ) : null;
                }}
              </Form.Item>

              <Form.Item noStyle shouldUpdate>
                {({ getFieldValue }) => {
                  const queryType = getFieldValue('queryType');
                  return queryType === 'day' ? (
                    <Form.Item name="day" label="日期" style={{ marginBottom: '8px' }}>
                      <DatePicker
                        placeholder="选择日期"
                        style={{ width: 120 }}
                        format="YYYY-MM-DD"
                        allowClear={false}
                      />
                    </Form.Item>
                  ) : null;
                }}
              </Form.Item>

              <Form.Item noStyle shouldUpdate>
                {({ getFieldValue }) => {
                  const queryType = getFieldValue('queryType');
                  return (queryType === 'all' || queryType === 'year' || queryType === 'month' || queryType === 'day') ? (
                    <Form.Item name="userId" label="用户" style={{ marginBottom: '8px' }}>
                      <Select
                        placeholder="输入客户昵称/用户名搜索"
                        style={{ width: 200 }}
                        allowClear
                        showSearch
                        filterOption={false}
                        onSearch={debouncedUserSearch}
                        loading={userLoading}
                        onPopupScroll={handleUserPopupScroll}
                        notFoundContent={
                          userLoading ? (
                            <Spin size="small" />
                          ) : (
                            <Empty
                              image={Empty.PRESENTED_IMAGE_SIMPLE}
                              description="暂无数据"
                            />
                          )
                        }
                      >
                        {userOptions.map((user) => (
                          <Select.Option key={user.id} value={user.id} title={user.nickname}>
                            {user.nickname} ({user.username})
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  ) : null;
                }}
              </Form.Item>

              <Form.Item style={{ marginBottom: '8px' }}>
                <Button 
                  type="primary" 
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                  loading={queryLoading}
                >
                  查询
                </Button>
              </Form.Item>
            </Form>

            {/* 汇总信息 */}
            {queryData?.summary && (
              <Row gutter={[16, 8]} style={{ marginBottom: '16px' }}>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="总发货量"
                    value={queryData.summary.totalShipments}
                    suffix="单"
                    valueStyle={{ color: '#1890ff', fontSize: '16px' }}
                    prefix={<BarChartOutlined />}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="总运费"
                    value={queryData.summary.totalFee}
                    suffix="元"
                    precision={2}
                    valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                    prefix={<DollarOutlined />}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="用户数"
                    value={queryData.summary.userCount}
                    suffix="个"
                    valueStyle={{ color: '#faad14', fontSize: '16px' }}
                    prefix={<UserOutlined />}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '12px', color: '#999' }}>统计范围</div>
                    <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                      {queryData.summary.dateRange}
                    </div>
                  </div>
                </Col>
              </Row>
            )}

            {/* 结果展示 */}
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={14}>
                <Spin spinning={queryLoading}>
                  {queryData?.statistics && queryData.statistics.length > 0 ? (
                    <Table
                      columns={tableColumns}
                      dataSource={queryData.statistics}
                      rowKey="id"
                      size="small"
                      pagination={{
                        pageSize: 8,
                        showSizeChanger: false,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 条记录`,
                      }}
                      scroll={{ x: 'max-content' }}
                      onChange={handleTableChange}
                    />
                  ) : (
                    <Empty 
                      description="暂无数据" 
                      style={{ padding: '40px 0' }}
                    />
                  )}
                </Spin>
              </Col>
              <Col xs={24} lg={10}>
                <Card 
                  size="small" 
                  style={{ minHeight: '450px' }}
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <span>数据分析</span>
                      <Radio.Group 
                        value={activeChart} 
                        onChange={(e: any) => setActiveChart(e.target.value)}
                        size="small"
                      >
                        <Radio.Button value="shipment">发货量排名</Radio.Button>
                        <Radio.Button value="fee">运费分布</Radio.Button>
                      </Radio.Group>
                    </div>
                  }
                >
                  {queryData?.statistics && queryData.statistics.length > 0 ? (
                    activeChart === 'shipment' ? (
                      <ShipmentChart data={queryData.statistics} />
                    ) : (
                      <FeeChart data={queryData.statistics} />
                    )
                  ) : (
                    <Empty 
                      image={Empty.PRESENTED_IMAGE_SIMPLE} 
                      description="暂无数据" 
                      style={{ margin: '60px 0' }}
                    />
                  )}
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 2. 快捷入口 和 待办事项 */}
      <Row gutter={[16, 16]} className={styles.actionsRow}>
        <Col xs={24} lg={8}>
          <Card
            title="快捷入口"
            bordered={false}
            className={styles.fullHeightCard}
          >
            <Space direction="vertical" style={{ width: "100%" }}>
              <Button type="primary" icon={<PlusOutlined />} block>
                创建新订单
              </Button>
              <Button icon={<SearchOutlined />} block>
                查询运单
              </Button>
              <Button icon={<FileTextOutlined />} block>
                录入包裹
              </Button>
              <Button block>查看库存</Button>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={16}>
          <Card
            title="待办事项 / 任务列表"
            bordered={false}
            className={styles.fullHeightCard}
          >
            <List
              itemLayout="horizontal"
              dataSource={todoData}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button type="link" size="small">
                      查看
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<UserOutlined />} // Can use a more specific icon
                    title={<a href="#">{item.title}</a>}
                    description={`优先级: ${item.priority.toUpperCase()} - 状态: ${
                      item.status
                    }`}
                  />
                  {item.priority === "high" && <Tag color="red">高</Tag>}
                  {item.priority === "medium" && <Tag color="orange">中</Tag>}
                  {item.priority === "low" && <Tag color="blue">低</Tag>}
                </List.Item>
              )}
            />
            <Button type="link" style={{ float: "right", marginTop: "10px" }}>
              查看全部
            </Button>
          </Card>
        </Col>
      </Row>

      {/* 3. 图表展示区 */}
      <Row gutter={[16, 16]} className={styles.chartsRow}>
        <Col xs={24} lg={12}>
          <Card title="运单状态分布" bordered={false}>
            <PieChartPlaceholder />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="近期订单量趋势" bordered={false}>
            <LineChartPlaceholder />
          </Card>
        </Col>
      </Row>

      {/* 4. 最新动态 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Card title="最新动态" bordered={false}>
            <List
              dataSource={activityData}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />} // More dynamic avatar if available
                    title={`${item.user} ${item.action}`}
                    description={item.time}
                  />
                </List.Item>
              )}
            />
            <Button type="link" style={{ float: "right", marginTop: "10px" }}>
              查看更多
            </Button>
          </Card>
        </Col>
      </Row>

      {/* 5. 全球货运地图 (可选) - 占位符 */}
      {/*
       <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
            <Col span={24}>
                <Card title="全球货运地图" bordered={false}>
                    <div style={{ height: '300px', background: '#f0f2f5', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <Text type="secondary">(地图可视化区域)</Text>
                    </div>
                </Card>
            </Col>
       </Row>
       */}
    </div>
  );
};

export default Dashboard;
