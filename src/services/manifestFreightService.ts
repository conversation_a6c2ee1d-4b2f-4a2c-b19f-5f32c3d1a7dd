import { ApiResponse, apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 根据运单ID计算运费 - 请求参数
export interface CalculateFreightByManifestRequest {
  manifestId: number; // 运单ID
  cargoType?: number; // 货物类型，可选择覆盖运单原有类型
}

// 运费模板信息
export interface FreightTemplate {
  id: number;
  name: string;
  firstWeightPrice: number; // 首重价格
  firstWeightRange: number; // 首重范围（公斤）
  continuedWeightPrice: number; // 续重价格
  continuedWeightInterval: number; // 续重区间大小（公斤）
  bulkCoefficient: number; // 轻抛系数
  threeSidesStart: number; // 三边和超过多少开始计算体积重量（厘米）
}

// 运费计算详情
export interface ManifestCalculationDetails {
  actualWeight: number; // 实际重量
  length: number; // 长度
  width: number; // 宽度
  height: number; // 高度
  threeSidesSum: number; // 三边和
  dimensionalWeight: number; // 体积重量
  chargeableWeight: number; // 计费重量
  firstWeightFee: number; // 首重费用
  continuedWeightFee: number; // 续重费用
  exceededWeight: number; // 超重重量
  continuedIntervals: number; // 续重区间数
}

// 根据运单ID计算运费 - 响应数据
export interface CalculateFreightByManifestData {
  manifestId: number;
  expressNumber: string;
  usedCargoType: number;
  usedCargoTypeName: string;
  template: FreightTemplate;
  calculationDetails: ManifestCalculationDetails;
  totalFreight: number;
}

// 修改运单模板类型 - 请求参数
export interface UpdateManifestTemplateTypeRequest {
  id: number; // 运单ID
  templateType: number; // 新的模板类型
}

// 修改运单模板类型 - 响应数据
export interface UpdateManifestTemplateTypeData {
  id: number;
  expressNumber: string;
  oldTemplateType: number;
  oldTemplateTypeName: string;
  newTemplateType: number;
  newTemplateTypeName: string;
  oldCost: number;
  newCost: number;
  costDifference: number;
}

/**
 * 根据运单ID计算运费
 * @param request 计算请求参数
 * @returns 计算结果
 */
export const calculateFreightByManifest = async (
  request: CalculateFreightByManifestRequest
): Promise<CalculateFreightByManifestData> => {
  try {
    const response = await apiClient.post<
      ApiResponse<CalculateFreightByManifestData>
    >(getApiPath("/freight/calculate-by-manifest"), request);

    if (!response.data.data) {
      throw new Error("运费计算失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("根据运单ID计算运费失败:", error);
    throw error;
  }
};

/**
 * 修改运单模板类型
 * @param request 修改请求参数
 * @returns 修改结果
 */
export const updateManifestTemplateType = async (
  request: UpdateManifestTemplateTypeRequest
): Promise<UpdateManifestTemplateTypeData> => {
  try {
    const response = await apiClient.post<
      ApiResponse<UpdateManifestTemplateTypeData>
    >(getApiPath("/manifests/update-template-type"), request);

    if (!response.data.data) {
      throw new Error("修改运单模板类型失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("修改运单模板类型失败:", error);
    throw error;
  }
};

// 货物类型选项
export const CARGO_TYPE_OPTIONS = [
  { value: 1, label: "普通货物" },
  { value: 2, label: "带电货物" },
  { value: 3, label: "投函货物" },
  { value: 6, label: "特殊货物" },
];

/**
 * 格式化货物类型
 * @param cargoType 货物类型
 * @returns 货物类型名称
 */
export const formatCargoType = (cargoType: number): string => {
  const typeMap = {
    1: "普通货物",
    2: "带电货物",
    3: "投函货物",
    6: "特殊货物",
  };
  return typeMap[cargoType as keyof typeof typeMap] || "未知类型";
};
