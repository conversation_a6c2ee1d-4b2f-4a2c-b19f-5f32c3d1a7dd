import { ApiResponse, apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 运费模板信息接口
export interface FreightTemplate {
  id: number;
  name: string;
  firstWeightPrice: number; // 首重价格
  firstWeightRange: number; // 首重范围（公斤）
  continuedWeightPrice: number; // 续重价格
  continuedWeightInterval: number; // 续重区间大小（公斤）
  bulkCoefficient: number; // 轻抛系数
  threeSidesStart: number; // 三边和超过多少开始计算体积重量（厘米）
}

// 运费计算详情接口
export interface FreightCalculationDetails {
  actualWeight: number; // 实际重量
  threeSidesSum: number; // 三边和
  dimensionalWeight: number; // 体积重量
  chargeableWeight: number; // 计费重量
  firstWeightFee: number; // 首重费用
  continuedWeightFee: number; // 续重费用
  continuedWeightIntervals: number; // 续重区间数
}

// 根据用户计算运费 - 请求参数
export interface CalculateFreightByUserRequest {
  userId: number; // 用户ID
  cargoType: number; // 货物类型：1-普通、2-带电、3-投函、6-特殊
  weight: number; // 重量，单位：公斤
  length?: number; // 长度，单位：厘米
  width?: number; // 宽度，单位：厘米
  height?: number; // 高度，单位：厘米
}

// 根据用户计算运费 - 响应数据
export interface CalculateFreightByUserData {
  userId: number;
  cargoType: number;
  template: FreightTemplate;
  calculationDetails: FreightCalculationDetails;
  totalFreight: number;
}

// 根据模板计算运费 - 请求参数
export interface CalculateFreightByTemplateRequest {
  templateId: number; // 运费模板ID
  weight: number; // 重量，单位：公斤
  length?: number; // 长度，单位：厘米
  width?: number; // 宽度，单位：厘米
  height?: number; // 高度，单位：厘米
}

// 根据模板计算运费 - 响应数据
export interface CalculateFreightByTemplateData {
  templateId: number;
  template: FreightTemplate;
  calculationDetails: FreightCalculationDetails;
  totalFreight: number;
}

/**
 * 根据用户计算运费
 * @param request 计算请求参数
 * @returns 计算结果
 */
export const calculateFreightByUser = async (
  request: CalculateFreightByUserRequest
): Promise<CalculateFreightByUserData> => {
  try {
    const response = await apiClient.post<
      ApiResponse<CalculateFreightByUserData>
    >(getApiPath("/freight/calculate"), request);

    if (!response.data.data) {
      throw new Error("运费计算失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("根据用户计算运费失败:", error);
    throw error;
  }
};

/**
 * 根据模板计算运费
 * @param request 计算请求参数
 * @returns 计算结果
 */
export const calculateFreightByTemplate = async (
  request: CalculateFreightByTemplateRequest
): Promise<CalculateFreightByTemplateData> => {
  try {
    const response = await apiClient.post<
      ApiResponse<CalculateFreightByTemplateData>
    >(getApiPath("/freight/calculate-by-template"), request);

    if (!response.data.data) {
      throw new Error("运费计算失败");
    }

    return response.data.data;
  } catch (error) {
    console.error("根据模板计算运费失败:", error);
    throw error;
  }
};

// 货物类型选项
export const CARGO_TYPE_OPTIONS = [
  { value: 1, label: "普通货物" },
  { value: 2, label: "带电货物" },
  { value: 3, label: "投函货物" },
  { value: 6, label: "特殊货物" },
];

/**
 * 格式化货物类型
 * @param cargoType 货物类型
 * @returns 货物类型名称
 */
export const formatCargoType = (cargoType: number): string => {
  const typeMap = {
    1: "普通货物",
    2: "带电货物",
    3: "投函货物",
    6: "特殊货物",
  };
  return typeMap[cargoType as keyof typeof typeMap] || "未知类型";
};
