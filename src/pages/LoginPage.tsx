import React, { useState } from "react";
import {
  Row,
  Col,
  Form,
  Input,
  Button,
  Checkbox,
  Typography,
  message,
  notification,
  Alert,
} from "antd";
import { LockOutlined, UserOutlined, LoadingOutlined } from "@ant-design/icons";
import {
  loginApiRaw,
  LoginPayload,
  LoginResponseData,
} from "../services/authService";
import { ApiResponse } from "../services/apiClient";
import { useNavigate } from "react-router-dom";
// import "./LoginPage.css"; // 确认移除

const { Title, Text, Link } = Typography;

// 恢复 LoginPageProps 接口
interface LoginPageProps {
  onLogin: () => void; // 接收从 App.tsx 传递的 onLogin 函数
}

// 恢复接收 onLogin prop
const LoginPage: React.FC<LoginPageProps> = ({ onLogin }) => {
  const [loading, setLoading] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const navigate = useNavigate();
  const [form] = Form.useForm();

  const onFinish = async (values: LoginPayload) => {
    console.log("表单提交的值:", values);
    setLoading(true);
    setLoginError(null);

    try {
      console.log("正在调用登录API...");
      const response: ApiResponse<LoginResponseData> = await loginApiRaw({
        username: values.username,
        password: values.password,
      });

      console.log("API响应:", JSON.stringify(response, null, 2));

      if (response.success && response.data?.token) {
        console.log("登录成功，token:", response.data.token);

        message.success("登录成功!");
        notification.success({
          message: "登录成功",
          description: "欢迎回到斑马物巢管理系统",
          duration: 3,
        });

        localStorage.setItem("authToken", response.data.token);

        if (response.data.userInfo) {
          console.log("保存用户信息:", response.data.userInfo);
          localStorage.setItem(
            "userInfo",
            JSON.stringify(response.data.userInfo)
          );
        } else {
          console.warn(
            "警告: 登录响应的 data 中没有找到 userInfo，将使用用户名作为昵称"
          );
          const basicUserInfo = {
            id: 0,
            username: values.username || "UnknownUser",
            nickname: values.username || "用户",
          };
          localStorage.setItem("userInfo", JSON.stringify(basicUserInfo));
        }

        onLogin();
        navigate("/");
      } else {
        console.warn("登录失败，业务错误:", response.errorMessage);
        const errorMsg = response.errorMessage || "登录失败，请检查您的凭据。";
        message.error(errorMsg);
        notification.error({
          message: "登录失败",
          description: errorMsg,
          duration: 4,
        });
        setLoginError(errorMsg);
      }
    } catch (error) {
      console.error("登录请求失败，捕获到异常:", error);
      const errorMsg =
        error instanceof Error
          ? `登录失败: ${error.message}`
          : "登录请求失败，请稍后重试。";
      message.error(errorMsg);
      notification.error({
        message: "登录请求错误",
        description: errorMsg,
        duration: 4,
      });
      setLoginError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  // --- JSX 结构保持不变 ---
  return (
    <Row className="login-page">
      {/* 左侧品牌区 */}
      <Col xs={0} sm={0} md={10} lg={10} xl={10} className="brand-section">
        <div className="brand-content">
          {/* 需要替换为实际 Logo */}
          <img
            src="./logo.116606f2.png" // 更新 Logo 路径
            alt="斑马物巢 Logo"
            className="brand-logo"
          />
          {/* 背景地图元素可以添加到 CSS 背景中 */}
          <Title level={2} className="brand-title">
            斑马物巢
          </Title>
          <Text className="brand-subtitle">智慧连接全球物流</Text>
          <Text className="brand-tagline">安全、高效、可追溯</Text>
        </div>
        {/* 斑马条纹图案可以通过 CSS 背景实现 */}
      </Col>

      {/* 右侧登录区 */}
      <Col xs={24} sm={24} md={14} lg={14} xl={14} className="login-section">
        <div className="login-form-container">
          <Title level={3} className="login-greeting">
            欢迎回到斑马物巢
          </Title>
          <Text type="secondary" className="login-system-name">
            跨境物流管理系统
          </Text>

          {/* 显示登录错误信息 */}
          {loginError && (
            <Alert
              message={loginError}
              type="error"
              showIcon
              closable
              style={{ marginBottom: 24 }}
              onClose={() => setLoginError(null)}
            />
          )}

          <Form
            form={form}
            name="normal_login"
            className="login-form"
            initialValues={{ remember: true }}
            onFinish={onFinish}
            size="large" // 增大输入框和按钮
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: "请输入您的用户名或邮箱!" }]}
            >
              <Input
                prefix={<UserOutlined className="site-form-item-icon" />}
                placeholder="用户名 / 邮箱"
              />
            </Form.Item>
            <Form.Item
              name="password"
              rules={[{ required: true, message: "请输入您的密码!" }]}
            >
              <Input.Password
                prefix={<LockOutlined className="site-form-item-icon" />}
                type="password"
                placeholder="密码"
              />
            </Form.Item>
            <Form.Item>
              <Row justify="space-between">
                <Col>
                  <Form.Item name="remember" valuePropName="checked" noStyle>
                    <Checkbox>记住登录信息</Checkbox>
                  </Form.Item>
                </Col>
                <Col>
                  <Link href="#" className="login-form-forgot">
                    忘记密码
                  </Link>
                </Col>
              </Row>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className="login-form-button"
                block
                loading={loading}
                icon={loading ? <LoadingOutlined /> : null}
              >
                {loading ? "登录中..." : "登 录"}
              </Button>
            </Form.Item>
          </Form>

          <div className="login-footer">
            <Text type="secondary" className="footer-text">
              © {new Date().getFullYear()} 斑马物巢
            </Text>
            <Text type="secondary" className="footer-text">
              版本号: v1.0.0 {/* TODO: 替换为实际版本号 */}
            </Text>
            {/* 社交按钮已在 CSS 中隐藏 */}
            {/* <Space className="social-icons">
              <GithubOutlined />
              <WechatOutlined />
              <GlobalOutlined />
            </Space> */}
          </div>
        </div>
      </Col>
    </Row>
  );
  // --- 恢复结束 ---
};

export default LoginPage;
