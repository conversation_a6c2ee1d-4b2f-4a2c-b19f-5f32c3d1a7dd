import { apiClient, ApiResponse, ApiError as AxiosApiError } from "./apiClient";
import { getApiPath } from "./apiPaths";
import type { AxiosError } from "axios";

export interface UserOption {
  id: number;
  nickname: string;
  username: string;
}

export interface UserOptionsResponseData {
  total: number;
  list: UserOption[];
}

export interface UserOptionsParams {
  keyword?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 获取用户选项列表 (用于下拉筛选)
 * @param params 查询参数 (关键字, 分页)
 * @returns 用户选项列表数据
 */
export const fetchUserOptions = async (
  params?: UserOptionsParams
): Promise<ApiResponse<UserOptionsResponseData>> => {
  try {
    const response = await apiClient.get<ApiResponse<UserOptionsResponseData>>(
      getApiPath("/users/options"),
      { params }
    );
    const apiResponseData = response.data;

    if (apiResponseData && apiResponseData.data) {
      return apiResponseData;
    }
    return {
      success: apiResponseData?.success || false,
      errorCode: apiResponseData?.errorCode || -1,
      errorMessage:
        apiResponseData?.errorMessage ||
        "Response data is missing or malformed",
      requestId: apiResponseData?.requestId || "",
      timestamp: apiResponseData?.timestamp || new Date().toISOString(),
      data: { total: 0, list: [] },
    };
  } catch (error) {
    console.error("获取用户选项列表失败 (userService catch):", error);

    if (error instanceof AxiosApiError) {
      return {
        success: false,
        errorCode: error.errorCode,
        errorMessage: error.message,
        requestId: "",
        timestamp: new Date().toISOString(),
        data: { total: 0, list: [] },
      };
    } else {
      const axiosError = error as AxiosError<
        ApiResponse<UserOptionsResponseData>
      >;
      return {
        success: false,
        errorCode:
          axiosError.response?.data?.errorCode ||
          axiosError.response?.status ||
          500,
        errorMessage:
          axiosError.response?.data?.errorMessage ||
          axiosError.message ||
          "获取用户选项列表时发生网络或服务器错误",
        requestId: axiosError.response?.data?.requestId || "",
        timestamp: new Date().toISOString(),
        data: { total: 0, list: [] },
      };
    }
  }
};
