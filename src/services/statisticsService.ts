import { apiClient, ApiResponse } from './apiClient';
import { getApiPath } from './apiPaths';

// 统计重新生成参数接口
export interface RegenerateStatisticsParams {
  year?: number;
  month?: number;
  day?: number;
}

// 统计重新生成响应数据接口
export interface RegenerateStatisticsData {
  message: string;
  startDate: string;
  endDate: string;
  processedDays: number;
  totalUsers: number;
  totalShipments: number;
  totalFee: number;
  duration: string;
}

// 统计查询参数接口
export interface QueryStatisticsParams {
  queryType: 'all' | 'year' | 'month' | 'day';
  year?: number;
  month?: number;
  day?: number;
  userId?: number;
  sortBy?: 'shipment' | 'fee';
  sortOrder?: 'desc' | 'asc';
}

// 统计数据项接口
export interface StatisticItem {
  id: number;
  statisticDate: string;
  userId: number;
  userNickname: string;
  shipmentCount: number;
  totalFee: number;
  createTime: string;
}

// 统计汇总信息接口
export interface StatisticsSummary {
  totalShipments: number;
  totalFee: number;
  userCount: number;
  dateRange: string;
}

// 统计查询响应接口
export interface QueryStatisticsResponse {
  statistics: StatisticItem[];
  totalRecords: number;
  summary: StatisticsSummary;
}

// 重新统计发货量数据
export const regenerateStatistics = async (
  params: RegenerateStatisticsParams = {}
): Promise<RegenerateStatisticsData> => {
  console.log('调用重新统计API，参数:', params);
  
  try {
    const response = await apiClient.post<ApiResponse<RegenerateStatisticsData>>(
      getApiPath('/statistics/regenerate'),
      {},  // 这个接口的参数通过query传递，不是body
      { params }
    );
    
    console.log('重新统计API响应:', response);
    
    // apiClient 返回的是完整的 AxiosResponse，需要访问 response.data
    const apiResponse: ApiResponse<RegenerateStatisticsData> = response.data;
    
    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse.errorMessage || "重新统计失败");
    }
  } catch (error) {
    console.error("Error regenerating statistics:", error);
    throw error;
  }
};

// 查询统计数据
export const queryStatistics = async (
  params: QueryStatisticsParams
): Promise<QueryStatisticsResponse> => {
  console.log('调用统计查询API，参数:', params);
  
  try {
    const response = await apiClient.get<ApiResponse<QueryStatisticsResponse>>(
      getApiPath('/statistics/query'),
      { params }
    );
    
    console.log('统计查询API响应:', response);
    
    const apiResponse: ApiResponse<QueryStatisticsResponse> = response.data;
    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse.errorMessage || '查询统计数据失败');
    }
  } catch (error: any) {
    console.error('查询统计数据API调用失败:', error);
    throw new Error(error.message || '查询统计数据失败');
  }
}; 