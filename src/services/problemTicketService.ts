import { apiClient, ApiResponse } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 新增：问题运单查询参数接口 (基于新接口文档)
export interface ProblemManifestQueryParams {
  expressNumber?: string; // 物流单号（模糊查询）
  userId?: number; // 客户ID
  shipmentTimeStart?: string; // 发货开始时间
  shipmentTimeEnd?: string; // 发货结束时间
  problemType?: number; // 问题类型（16或-1）
  page?: number; // 页码（默认1）
  pageSize?: number; // 每页数量（默认10）
}

// 新增：问题运单接口 (基于新接口文档)
export interface ProblemManifest {
  id: number;
  expressNumber: string;
  trackingStatus: number;
  trackingStatusName: string;
  userId: number;
  userNickname: string;
  shipmentTime: string;
  createTime: string;
  updateTime: string;
}

// 新增：问题运单列表响应数据
export interface ProblemManifestListData {
  total: number;
  list: ProblemManifest[];
}

// 新增：问题类型选项
export const PROBLEM_TYPE_OPTIONS = [
  { value: "", label: "全部问题类型" },
  { value: 16, label: "持戻り" },
  { value: -1, label: "営業所へお問い合わせください" },
];

// 新增：获取问题运单列表 (调用新接口)
export const getProblemManifests = async (
  params?: ProblemManifestQueryParams
): Promise<ProblemManifestListData> => {
  try {
    const response = await apiClient.get(
      getApiPath("/manifests/problem-manifests"),
      { params }
    );

    // apiClient 返回的是完整的 AxiosResponse，需要访问 response.data
    const apiResponse: ApiResponse<ProblemManifestListData> = response.data;

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse.errorMessage || "获取问题运单列表失败");
    }
  } catch (error) {
    console.error("Error fetching problem manifests:", error);
    throw error;
  }
};

// 问题订单查询参数接口
export interface ProblemTicketQueryParams {
  page?: number;
  pageSize?: number;
  status?: string;
  problemTypeCode?: string;
  trackingNumber?: string;
  customerId?: number;
  assignedUserId?: number;
  startTime?: string;
  endTime?: string;
}

// 问题订单接口
export interface ProblemTicket {
  id: number;
  manifestId: number;
  trackingNumber: string;
  customerAccountId: number;
  problemTypeCode: string;
  problemDescription: string;
  status: string;
  priority: number;
  assignedToUserId: number | null;
  remarks: string | null;
  createTime: string;
  updateTime: string;
  resolvedTime: string | null;
  customerNickname: string;
  assignedUserNickname: string | null;
}

// 问题订单列表响应数据
export interface ProblemTicketListData {
  total: number;
  page: number;
  pageSize: number;
  data: ProblemTicket[];
}

// 工单状态枚举
export const TICKET_STATUS_OPTIONS = [
  { value: "PENDING", label: "待处理" },
  { value: "PENDING_ACTION", label: "待处理" },
  { value: "IN_PROGRESS", label: "处理中" },
  { value: "RESOLVED", label: "已处理" },
  { value: "CLOSED_UNRESOLVED", label: "关闭(未解决)" },
];

// 优先级映射
export const PRIORITY_MAP = {
  1: { color: "#52c41a", text: "低" },
  2: { color: "#faad14", text: "中" },
  3: { color: "#ff4d4f", text: "高" },
};

// 新增：清单内物品接口
export interface ManifestItem {
  weight: number;
  quantity: number;
  price: number;
  name: string;
  value: number;
}

// 费用调整明细接口
export interface FinancialAdjustment {
  id: number; // 调整记录ID
  adjustmentType: string; // 调整类型
  description: string; // 调整描述/原因
  amount: number; // 调整金额 (正数表示收入, 负数表示支出/赔偿)
  currency: string; // 货币单位
  effectiveDate: string; // 费用实际发生/确认日期
  customerAccountId: number; // 客户ID
  attachmentPaths: string; // 附件路径列表
  creatorId: number; // 创建者ID
  creatorNickname: string; // 创建者昵称
  createTime: string; // 创建时间
  isVoid: boolean; // 是否作废，true表示作废，false表示生效
  is_void?: boolean; // 后端可能返回的字段名，运行时会转换为 isVoid
}

// 新增：清单详情数据接口 (对应后端 /api/v1/manifests/{id} 的 data 部分)
export interface ManifestDetailData {
  id: number;
  expressNumber: string;
  orderNumber: string; // 商家订单号
  orderNo?: string; // 系统订单号
  receiverZipCode: string;
  receiverName: string;
  receiverAddress: string;
  receiverPhone: string;
  items: ManifestItem[];
  validateStatus: number;
  validateError: string;
  createTime: string;
  userId: number;
  userNickname: string;
  preRegistrationBatchId: string;

  // 包裹信息 (新增)
  length?: number; // 长 (cm)
  width?: number; // 宽 (cm)
  height?: number; // 高 (cm)
  weight?: number; // 实际重量 (kg)
  dimensionalWeight?: number; // 体积重量 (kg)

  // 费用信息 (新增)
  cost?: number; // 基本费用
  overLengthSurcharge?: number; // 超长费
  remoteAreaSurcharge?: number; // 偏远费
  totalFee?: number; // 总费用
  currency?: string; // 货币单位 (新增)

  // 运费模板和货物类型信息 (新增)
  destinationCode?: string; // 目的地代码
  destinationName?: string; // 目的地名称 (如：东京、大阪)
  shippingFeeTemplateType?: number; // 运费模板类型
  shippingFeeTemplateTypeName?: string; // 运费模板类型完整名称
  cargoType?: number; // 货物类型 (1-普通、2-带电、3-投函、6-特殊)
  cargoTypeName?: string; // 货物类型名称

  // 费用调整明细 (新增)
  financialAdjustments?: FinancialAdjustment[]; // 费用调整记录列表
}

// 新增：单条物流轨迹信息接口
export interface TrackingEvent {
  id: number;
  manifestId: number;
  operatorId: number;
  status: number; // 或根据实际情况定义为更具体的类型，如字符串枚举
  track: string;
  place: string;
  time: string | null; // API示例中为null，但也可能为日期字符串
  createTime: string;
  updateTime: string;
}

// 新增：运单物流轨迹响应数据接口
export interface ManifestTrackingData {
  manifestId: number;
  trackingCount: number;
  trackings: TrackingEvent[];
}

// 新增：处理问题订单请求体
export interface ResolveTicketPayload {
  remarks?: string;
}

// 新增：批量处理问题订单请求体
export interface ResolveAllTicketsPayload {
  remarks?: string;
}

// 新增：批量处理问题订单响应数据
export interface ResolveAllTicketsResponse {
  processedCount: number;
}

// 新增：处理指定问题订单请求体
export interface ResolveSpecificTicketsPayload {
  ticketIds: React.Key[]; // 或者 number[] 如果ID确定是数字
  remarks?: string;
}

// 新增：处理指定问题订单响应数据
export interface ResolveSpecificTicketsResponse {
  processedCount: number;
}

// 新增：标记指定问题订单为待处理请求体
export interface MarkPendingTicketsPayload {
  ticketIds: React.Key[]; // 或者 number[]
  remarks?: string;
}

// 新增：标记指定问题订单为待处理响应数据
export interface MarkPendingTicketsResponse {
  processedCount: number;
}

// 新增：拥有问题运单的用户接口
export interface UserWithProblems {
  userId: number;
  nickname: string;
  pendingCount: number;
}

// 新增：拥有问题运单的用户列表响应数据
export interface UsersWithProblemsResponse {
  total: number;
  list: UserWithProblems[];
}

// 新增：获取拥有问题运单的用户列表查询参数
export interface UsersWithProblemsQueryParams {
  page?: number;
  pageSize?: number;
}

// 新增：问题运单数量响应数据
export interface ProblemManifestCountData {
  count: number;
}

// 新增：获取问题运单数量 (调用新接口)
export const getProblemManifestCount = async (
  params?: ProblemManifestQueryParams
): Promise<number> => {
  try {
    const response = await apiClient.get(
      getApiPath("/manifests/problem-manifests/count"),
      { params }
    );

    // apiClient 返回的是完整的 AxiosResponse，需要访问 response.data
    const apiResponse: ApiResponse<ProblemManifestCountData> = response.data;

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data.count;
    } else {
      throw new Error(apiResponse.errorMessage || "获取问题运单数量失败");
    }
  } catch (error) {
    console.error("Error fetching problem manifest count:", error);
    throw error;
  }
};

/**
 * 获取问题订单列表
 * @param params 查询参数
 * @returns 问题订单列表数据
 */
export const getProblemTickets = async (
  params?: ProblemTicketQueryParams
): Promise<ProblemTicketListData> => {
  const response = await apiClient.get<ApiResponse<ProblemTicketListData>>(
    getApiPath("/problem-tickets"),
    { params }
  );
  return response.data.data || { total: 0, page: 1, pageSize: 10, data: [] };
};

/**
 * 获取问题订单详情
 * @param id 问题订单ID
 * @returns 问题订单详情
 */
export const getProblemTicketDetail = async (
  id: number
): Promise<ProblemTicket> => {
  const response = await apiClient.get<ApiResponse<ProblemTicket>>(
    getApiPath(`/problem-tickets/${id}`)
  );
  return response.data.data as ProblemTicket;
};

/**
 * 获取清单详情 (用于问题订单详情展示)
 * @param manifestId 清单ID
 * @returns 清单详情数据
 */
export const getManifestDetailsByManifestId = async (
  manifestId: number
): Promise<ManifestDetailData> => {
  const response = await apiClient.get<ApiResponse<ManifestDetailData>>(
    getApiPath(`/manifests/${manifestId}`)
  );
  return response.data.data as ManifestDetailData;
};

/**
 * 新增：获取运单的物流轨迹信息
 * @param manifestId 运单ID
 * @returns 物流轨迹列表 (TrackingEvent[])
 */
export const getManifestTrackings = async (
  manifestId: number
): Promise<TrackingEvent[]> => {
  try {
    const response = await apiClient.get<ApiResponse<ManifestTrackingData>>(
      getApiPath(`/manifests/${manifestId}/trackings`)
    );
    if (
      response.data &&
      response.data.success &&
      response.data.data &&
      Array.isArray(response.data.data.trackings)
    ) {
      return response.data.data.trackings;
    }
    // 如果响应格式不符合预期或success为false，则抛出错误或返回空数组
    console.error("获取物流轨迹失败或响应格式不正确", response.data);
    throw new Error(
      response.data?.errorMessage || "获取物流轨迹失败，响应格式不正确"
    );
  } catch (error) {
    console.error(`获取运单 ${manifestId} 物流轨迹失败:`, error);
    // 根据项目错误处理策略，可以重新抛出错误或返回一个表示错误的状态
    // 例如，如果 error 是 ApiError 的实例，可以保留其原始信息
    if (error instanceof Error) {
      throw error; // 或者 throw new ApiError(error.message, (error as any).errorCode || 0);
    }
    throw new Error("获取物流轨迹时发生未知错误");
  }
};

/**
 * 处理（解决）问题订单
 * @param id 问题订单ID
 * @param payload 请求体，包含可选的备注信息
 * @returns 返回处理结果，具体结构待后端API定义，这里假设为通用成功响应
 */
export const resolveProblemTicket = async (
  id: number,
  payload: ResolveTicketPayload
): Promise<ApiResponse<unknown>> => {
  const response = await apiClient.put<ApiResponse<unknown>>(
    getApiPath(`/problem-tickets/${id}/resolve`),
    payload
  );
  return response.data;
};

/**
 * 更新问题订单备注
 * @param id 问题订单ID
 * @param remarks 新的备注内容
 * @returns 返回处理结果
 */
export const updateProblemTicketRemark = async (
  id: number,
  remarks: string
): Promise<ApiResponse<unknown>> => {
  const response = await apiClient.put<ApiResponse<unknown>>(
    getApiPath(`/problem-tickets/${id}/remark`),
    { remarks }
  );
  return response.data;
};

/**
 * 批量处理所有待处理的问题订单
 * @param payload 请求体，包含可选的备注信息
 * @returns 返回处理结果，包含已处理的工单数量
 */
export const resolveAllProblemTickets = async (
  payload: ResolveAllTicketsPayload
): Promise<ResolveAllTicketsResponse> => {
  const response = await apiClient.post<ApiResponse<ResolveAllTicketsResponse>>(
    getApiPath(`/problem-tickets/resolve-all`),
    payload
  );
  return response.data.data as ResolveAllTicketsResponse;
};

/**
 * 批量处理指定ID列表中的问题订单
 * @param payload 请求体，包含ticketIds和可选的备注信息
 * @returns 返回处理结果，包含已成功处理的工单数量
 */
export const resolveSpecificProblemTickets = async (
  payload: ResolveSpecificTicketsPayload
): Promise<ResolveSpecificTicketsResponse> => {
  const response = await apiClient.post<
    ApiResponse<ResolveSpecificTicketsResponse>
  >(getApiPath(`/problem-tickets/resolve-specific`), payload);
  if (response.data && response.data.success && response.data.data) {
    return response.data.data;
  }
  throw new Error(response.data?.errorMessage || "处理指定问题订单失败");
};

/**
 * 批量将指定ID列表中的问题工单标记为待处理状态
 * @param payload 请求体，包含ticketIds和可选的备注信息
 * @returns 返回处理结果，包含已成功处理的工单数量
 */
export const markSpecificTicketsAsPending = async (
  payload: MarkPendingTicketsPayload
): Promise<MarkPendingTicketsResponse> => {
  const response = await apiClient.post<
    ApiResponse<MarkPendingTicketsResponse>
  >(getApiPath(`/problem-tickets/mark-pending`), payload);
  if (response.data && response.data.success && response.data.data) {
    return response.data.data;
  }
  throw new Error(response.data?.errorMessage || "标记选中为待处理失败");
};

/**
 * 获取拥有问题运单的用户列表
 * @param params 查询参数，包含页码和每页数量
 * @returns 拥有问题运单的用户列表数据
 */
export const getUsersWithProblems = async (
  params?: UsersWithProblemsQueryParams
): Promise<UsersWithProblemsResponse> => {
  const response = await apiClient.get<ApiResponse<UsersWithProblemsResponse>>(
    getApiPath(`/problem-tickets/users-with-problems`),
    { params }
  );

  if (response.data.success && response.data.data) {
    return response.data.data;
  }

  return { total: 0, list: [] };
};

// 新增：发货量统计响应数据
export interface ShipmentCountData {
  count: number;
  date: string;
}

// 新增：获取本月发货量统计
export const getMonthlyShipmentCount = async (): Promise<ShipmentCountData> => {
  try {
    const response = await apiClient.get(
      getApiPath("/manifests/monthly-shipment-count")
    );

    const apiResponse: ApiResponse<ShipmentCountData> = response.data;

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse.errorMessage || "获取本月发货量失败");
    }
  } catch (error) {
    console.error("Error fetching monthly shipment count:", error);
    throw error;
  }
};

// 新增：获取今日发货量统计
export const getTodayShipmentCount = async (): Promise<ShipmentCountData> => {
  try {
    const response = await apiClient.get(
      getApiPath("/manifests/today-shipment-count")
    );

    const apiResponse: ApiResponse<ShipmentCountData> = response.data;

    if (apiResponse.success && apiResponse.data) {
      return apiResponse.data;
    } else {
      throw new Error(apiResponse.errorMessage || "获取今日发货量失败");
    }
  } catch (error) {
    console.error("Error fetching today shipment count:", error);
    throw error;
  }
};
