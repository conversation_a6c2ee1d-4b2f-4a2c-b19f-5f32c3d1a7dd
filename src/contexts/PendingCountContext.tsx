import React, { createContext, useContext, ReactNode } from "react";
import usePendingCountHook from "../hooks/usePendingCount"; // Renamed to avoid confusion

interface PendingCountContextType {
  pendingCount: number;
  loading: boolean;
  refreshPendingCount: () => Promise<void>;
}

const PendingCountContext = createContext<PendingCountContextType | undefined>(
  undefined
);

export const PendingCountProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const { pendingCount, loading, refresh } = usePendingCountHook();

  return (
    <PendingCountContext.Provider
      value={{ pendingCount, loading, refreshPendingCount: refresh }}
    >
      {children}
    </PendingCountContext.Provider>
  );
};

export const useSharedPendingCount = (): PendingCountContextType => {
  const context = useContext(PendingCountContext);
  if (context === undefined) {
    throw new Error(
      "useSharedPendingCount must be used within a PendingCountProvider"
    );
  }
  return context;
};
