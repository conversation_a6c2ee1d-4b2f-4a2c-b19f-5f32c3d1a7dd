# 数据管理功能说明

## 功能概述

数据管理模块是系统新增的数据维护功能，目前包含统计管理子功能，用于重新统计发货量数据。

## 功能特性

### 统计管理

统计管理页面提供了重新统计发货量数据的功能，支持以下特性：

- **多种统计范围**：支持全部历史数据、指定年份、指定月份、指定日期
- **参数验证**：确保输入的日期参数合法有效
- **实时反馈**：统计过程中显示加载状态，完成后展示详细结果
- **结果展示**：展示处理天数、涉及用户数、总发货量、总运费金额、处理耗时等信息

## 技术实现

### 文件结构

```
src/
├── services/
│   └── statisticsService.ts                    # 统计相关API服务
├── pages/
│   └── DataManagement/
│       └── StatisticsManagementPage.tsx        # 统计管理页面组件
├── layouts/
│   └── MainLayout.tsx                          # 更新菜单配置
└── App.tsx                                     # 更新路由配置
```

### API 接口

#### 重新统计发货量数据

- **接口路径**: `/api/v1/statistics/regenerate`
- **请求方法**: POST
- **权限要求**: 需要JWT认证

**请求参数**:
- `year` (可选): 年份，数字类型
- `month` (可选): 月份，1-12之间的数字
- `day` (可选): 日期，1-31之间的数字

**参数组合规则**:
1. 不传任何参数：统计从2020年1月1日到昨天的所有数据
2. 只传year：统计指定年份的所有数据
3. 传year和month：统计指定年月的所有数据
4. 传year、month和day：统计指定日期的数据

**响应数据**:
```typescript
{
  message: string;           // 执行结果消息
  startDate: string;         // 开始日期（YYYY-MM-DD格式）
  endDate: string;           // 结束日期（YYYY-MM-DD格式）
  processedDays: number;     // 处理的天数
  totalUsers: number;        // 涉及的用户数
  totalShipments: number;    // 总发货量（运单数）
  totalFee: number;          // 总运费金额
  duration: string;          // 处理耗时
}
```

### 数据类型

```typescript
// 统计重新生成参数接口
interface RegenerateStatisticsParams {
  year?: number;
  month?: number;
  day?: number;
}

// 统计重新生成响应数据接口
interface RegenerateStatisticsData {
  message: string;
  startDate: string;
  endDate: string;
  processedDays: number;
  totalUsers: number;
  totalShipments: number;
  totalFee: number;
  duration: string;
}
```

## 访问路径

- **菜单路径**: 数据管理 → 统计管理
- **URL 路径**: `/#/data-management/statistics`

## 使用说明

### 操作步骤

1. **进入页面**: 从左侧菜单点击"数据管理" → "统计管理"
2. **选择范围**: 选择统计范围类型（全部历史数据、指定年份、指定月份、指定日期）
3. **设置参数**: 根据选择的范围类型，设置相应的日期参数
4. **开始统计**: 点击"开始重新统计"按钮执行统计
5. **查看结果**: 等待统计完成后查看详细的统计结果

### 注意事项

- 重新统计会覆盖已有的统计数据
- 大范围统计可能需要较长时间，请耐心等待
- 建议在业务低峰期进行大范围统计操作
- 如果不选择任何范围，将统计从2020年1月1日到昨天的所有数据

## 使用场景

1. **系统初始化**: 首次部署系统后，统计历史数据
2. **数据修正**: 发现统计数据错误时，重新统计
3. **月度处理**: 每月初重新统计上月数据确保准确性
4. **故障恢复**: 定时任务失败后手动补充统计

## 后续优化

1. **批量操作**: 支持批量重新统计多个时间段
2. **任务队列**: 大范围统计任务放入队列异步处理
3. **进度显示**: 显示统计进度百分比
4. **历史记录**: 记录统计操作历史和结果
5. **导出功能**: 支持导出统计结果为Excel或PDF 