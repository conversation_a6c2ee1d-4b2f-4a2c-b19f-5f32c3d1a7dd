import { apiClient, ApiResponse, ApiError as AxiosApiError } from "./apiClient";
import { getApiPath } from "./apiPaths";
import type { AxiosError } from "axios";

export interface MasterBillOption {
  id: number;
  masterBillNumber: string;
}

export interface MasterBillOptionsResponseData {
  total: number;
  list: MasterBillOption[];
}

export interface MasterBillOptionsParams {
  keyword?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 获取提单选项列表 (用于下拉筛选)
 * @param params 查询参数 (关键字, 分页)
 * @returns 提单选项列表数据
 */
export const fetchMasterBillOptions = async (
  params?: MasterBillOptionsParams
): Promise<ApiResponse<MasterBillOptionsResponseData>> => {
  try {
    const response = await apiClient.get<
      ApiResponse<MasterBillOptionsResponseData>
    >(
      getApiPath("/master-bills/options"), // Path for master bills
      { params }
    );
    const apiResponseData = response.data;

    if (apiResponseData && apiResponseData.data) {
      return apiResponseData;
    }
    // Fallback for missing or malformed data
    return {
      success: apiResponseData?.success || false,
      errorCode: apiResponseData?.errorCode || -1,
      errorMessage:
        apiResponseData?.errorMessage ||
        "Response data is missing or malformed",
      requestId: apiResponseData?.requestId || "",
      timestamp: apiResponseData?.timestamp || new Date().toISOString(),
      data: { total: 0, list: [] },
    };
  } catch (error) {
    console.error("获取提单选项列表失败 (masterBillService catch):", error);

    if (error instanceof AxiosApiError) {
      return {
        success: false,
        errorCode: error.errorCode,
        errorMessage: error.message,
        requestId: "",
        timestamp: new Date().toISOString(),
        data: { total: 0, list: [] },
      };
    } else {
      const axiosError = error as AxiosError<
        ApiResponse<MasterBillOptionsResponseData>
      >;
      return {
        success: false,
        errorCode:
          axiosError.response?.data?.errorCode ||
          axiosError.response?.status ||
          500,
        errorMessage:
          axiosError.response?.data?.errorMessage ||
          axiosError.message ||
          "获取提单选项列表时发生网络或服务器错误",
        requestId: axiosError.response?.data?.requestId || "",
        timestamp: new Date().toISOString(),
        data: { total: 0, list: [] },
      };
    }
  }
};
