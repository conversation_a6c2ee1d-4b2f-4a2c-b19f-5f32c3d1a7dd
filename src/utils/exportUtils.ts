/**
 * 导出工具函数
 */

/**
 * 通用文件下载函数
 * @param blob 文件数据
 * @param filename 文件名
 */
export const downloadFile = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;

  // 触发下载
  document.body.appendChild(link);
  link.click();

  // 清理
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

/**
 * 从响应头获取文件名
 * @param contentDisposition Content-Disposition 响应头
 * @returns 文件名
 */
export const getFilenameFromContentDisposition = (
  contentDisposition: string
): string | null => {
  if (!contentDisposition) {
    return null;
  }

  const filenameMatch = contentDisposition.match(/filename=(.+)/);
  if (filenameMatch) {
    return decodeURIComponent(filenameMatch[1]);
  }

  return null;
};

/**
 * 生成默认的Excel文件名
 * @param prefix 文件名前缀
 * @param id 记录ID
 * @returns 文件名
 */
export const generateExcelFilename = (prefix: string, id: number): string => {
  const timestamp = new Date().toISOString().slice(0, 10);
  return `${prefix}_${id}_${timestamp}.xlsx`;
};
