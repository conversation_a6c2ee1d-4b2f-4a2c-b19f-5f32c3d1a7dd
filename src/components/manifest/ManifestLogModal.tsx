import React, { useState, useEffect } from "react";
import { Modal, Table, Tag, Typography, Descriptions, App, Empty } from "antd";
import {
  UserOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
  ManifestOperationLog,
  getManifestOperationLogs,
  OPERATION_TYPE_MAP,
  OperationType,
  ChangeDetails,
} from "../../services/manifestLogService";

const { Text, Paragraph } = Typography;

interface ManifestLogModalProps {
  visible: boolean;
  manifestId: number | null;
  onClose: () => void;
}

const ManifestLogModal: React.FC<ManifestLogModalProps> = ({
  visible,
  manifestId,
  onClose,
}) => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState<boolean>(false);
  const [logs, setLogs] = useState<ManifestOperationLog[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // 加载日志数据
  const loadLogs = async (page: number = 1, size: number = 10) => {
    if (!manifestId) return;

    try {
      setLoading(true);
      const result = await getManifestOperationLogs(manifestId, {
        page,
        pageSize: size,
      });
      setLogs(result.list);
      setTotal(result.total);
      setCurrent(page);
      setPageSize(size);
    } catch (error) {
      console.error("获取操作日志失败:", error);
      message.error("获取操作日志失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 当Modal打开且有manifestId时加载数据
  useEffect(() => {
    if (visible && manifestId) {
      loadLogs(1, 10);
    }
  }, [visible, manifestId]);

  // 处理分页变化
  const handlePageChange = (page: number, size: number) => {
    loadLogs(page, size);
  };

  // 获取操作类型标签颜色
  const getOperationTypeColor = (type: OperationType): string => {
    switch (type) {
      case OperationType.CREATE:
        return "blue";
      case OperationType.APPROVE:
        return "green";
      case OperationType.REJECT:
        return "red";
      case OperationType.UPDATE_TEMPLATE_TYPE:
        return "orange";
      case OperationType.ADJUST_FEE:
        return "purple";
      case OperationType.PICKUP:
        return "cyan";
      case OperationType.SHIP:
        return "geekblue";
      case OperationType.DELIVER:
        return "lime";
      case OperationType.MARK_EXCEPTION:
        return "volcano";
      default:
        return "default";
    }
  };

  // 渲染变更详情
  const renderChangeDetails = (details?: ChangeDetails) => {
    if (!details) return null;

    const items = [];

    // 模板类型变更
    if (details.oldTemplateTypeName && details.newTemplateTypeName) {
      items.push({
        key: "template",
        label: "模板类型",
        children: (
          <span>
            <Text delete>{details.oldTemplateTypeName}</Text>
            <Text type="success" style={{ marginLeft: 8 }}>
              {details.newTemplateTypeName}
            </Text>
          </span>
        ),
      });
    }

    // 费用变更
    if (details.oldCost !== undefined && details.newCost !== undefined) {
      items.push({
        key: "cost",
        label: "运费变更",
        children: (
          <span>
            <Text delete>¥{details.oldCost}</Text>
            <Text type="success" style={{ marginLeft: 8 }}>
              ¥{details.newCost}
            </Text>
            {details.costDifference && (
              <Text type="warning" style={{ marginLeft: 8 }}>
                ({details.costDifference > 0 ? "+" : ""}¥
                {details.costDifference})
              </Text>
            )}
          </span>
        ),
      });
    }

    // 状态变更
    if (details.statusName) {
      items.push({
        key: "status",
        label: "状态变更",
        children: <Text type="success">{details.statusName}</Text>,
      });
    }

    return items.length > 0 ? (
      <Descriptions size="small" column={1} items={items} />
    ) : null;
  };

  // 表格列定义
  const columns: ColumnsType<ManifestOperationLog> = [
    {
      title: "操作时间",
      dataIndex: "operationTime",
      key: "operationTime",
      width: 180,
      render: (time: string) => (
        <div style={{ display: "flex", flexDirection: "column" }}>
          <Text strong>{dayjs(time).format("YYYY-MM-DD")}</Text>
          <Text type="secondary" style={{ fontSize: "12px" }}>
            {dayjs(time).format("HH:mm:ss")}
          </Text>
        </div>
      ),
    },
    {
      title: "操作类型",
      dataIndex: "operationType",
      key: "operationType",
      width: 120,
      render: (type: OperationType) => (
        <Tag color={getOperationTypeColor(type)}>
          {OPERATION_TYPE_MAP[type] || type}
        </Tag>
      ),
    },
    {
      title: "操作人",
      dataIndex: "operatorName",
      key: "operatorName",
      width: 100,
      render: (name: string) => (
        <div style={{ display: "flex", alignItems: "center", gap: 4 }}>
          <UserOutlined style={{ color: "#1890ff" }} />
          <Text>{name}</Text>
        </div>
      ),
    },
    {
      title: "操作描述",
      dataIndex: "description",
      key: "description",
      render: (description: string, record: ManifestOperationLog) => (
        <div>
          <Paragraph
            ellipsis={{ rows: 2, expandable: true, symbol: "展开" }}
            style={{ marginBottom: record.changeDetails ? 8 : 0 }}
          >
            {description}
          </Paragraph>
          {renderChangeDetails(record.changeDetails)}
        </div>
      ),
    },
    {
      title: "来源/IP",
      key: "source",
      width: 120,
      render: (_, record: ManifestOperationLog) => (
        <div style={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <Tag>{record.context}</Tag>
          <Text type="secondary" style={{ fontSize: "11px" }}>
            <EnvironmentOutlined style={{ marginRight: 2 }} />
            {record.ipAddress}
          </Text>
        </div>
      ),
    },
  ];

  return (
    <Modal
      title={
        <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
          <FileTextOutlined />
          <span>运单操作日志</span>
          {manifestId && (
            <Text type="secondary" style={{ fontSize: "14px" }}>
              (运单ID: {manifestId})
            </Text>
          )}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      style={{ top: 20 }}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          <ClockCircleOutlined style={{ marginRight: 4 }} />
          按时间倒序显示，最新操作在前
        </Text>
      </div>

      {logs.length === 0 && !loading ? (
        <Empty description="暂无操作日志" />
      ) : (
        <Table
          rowKey="id"
          columns={columns}
          dataSource={logs}
          loading={loading}
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: handlePageChange,
            pageSizeOptions: ["10", "20", "50"],
          }}
          size="small"
          bordered
        />
      )}
    </Modal>
  );
};

export default ManifestLogModal;
