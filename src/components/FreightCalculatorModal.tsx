import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Modal,
  Form,
  Button,
  Select,
  Radio,
  Card,
  Row,
  Col,
  Descriptions,
  Typography,
  Space,
  App,
  Spin,
  Empty,
  InputNumber,
  Divider,
} from "antd";
import {
  CalculatorOutlined,
  UserOutlined,
  FileTextOutlined,
  CopyOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import html2canvas from "html2canvas";
import debounce from "../utils/debounce";

// 扩展html2canvas的配置选项类型
interface Html2CanvasExtendedOptions {
  backgroundColor?: string;
  scale?: number;
  useCORS?: boolean;
  allowTaint?: boolean;
  width?: number;
  height?: number;
}
import {
  calculateFreightByUser,
  calculateFreightByTemplate,
  CalculateFreightByUserRequest,
  CalculateFreightByTemplateRequest,
  CalculateFreightByUserData,
  CalculateFreightByTemplateData,
  CARGO_TYPE_OPTIONS,
  formatCargoType,
} from "../services/freightCalculatorService";
import { fetchUserOptions, UserOption } from "../services/userService";
import {
  getShippingFeeTemplates,
  ShippingFeeTemplate,
} from "../services/shippingFeeService";

const { Text } = Typography;
const { Option } = Select;

interface FreightCalculatorModalProps {
  visible: boolean;
  onClose: () => void;
}

type CalculationMode = "user" | "template";

const ITEM_PAGE_SIZE = 20;

const FreightCalculatorModal: React.FC<FreightCalculatorModalProps> = ({
  visible,
  onClose,
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [calculationMode, setCalculationMode] =
    useState<CalculationMode>("user");
  const [calculating, setCalculating] = useState<boolean>(false);
  const [generatingImage, setGeneratingImage] = useState<boolean>(false);
  const resultCardRef = useRef<HTMLDivElement>(null);

  // 用户选择相关状态
  const [userOptions, setUserOptions] = useState<UserOption[]>([]);
  const [userLoading, setUserLoading] = useState<boolean>(false);
  const [userPage, setUserPage] = useState<number>(1);
  const [userTotal, setUserTotal] = useState<number>(0);

  // 模板选择相关状态
  const [templateOptions, setTemplateOptions] = useState<ShippingFeeTemplate[]>(
    []
  );
  const [templateLoading, setTemplateLoading] = useState<boolean>(false);

  // 计算结果状态
  const [calculationResult, setCalculationResult] = useState<
    CalculateFreightByUserData | CalculateFreightByTemplateData | null
  >(null);

  // 加载用户选项
  const loadUserOptions = useCallback(
    async (keyword: string, page: number, append: boolean = false) => {
      setUserLoading(true);
      try {
        const apiResponse = await fetchUserOptions({
          keyword,
          page,
          pageSize: ITEM_PAGE_SIZE,
        });

        if (apiResponse.success && apiResponse.data) {
          const { list, total } = apiResponse.data;
          if (Array.isArray(list)) {
            setUserOptions((prevOptions) =>
              append ? [...prevOptions, ...list] : list
            );
            setUserTotal(total);
            setUserPage(page);
          } else {
            if (!append) {
              setUserOptions([]);
              setUserTotal(0);
            }
          }
        } else {
          if (!append) {
            setUserOptions([]);
            setUserTotal(0);
          }
        }
      } catch (error) {
        console.error("获取用户列表失败:", error);
        if (!append) {
          setUserOptions([]);
          setUserTotal(0);
        }
      } finally {
        setUserLoading(false);
      }
    },
    []
  );

  // 加载模板选项
  const loadTemplateOptions = useCallback(async (keyword: string = "") => {
    setTemplateLoading(true);
    try {
      const response = await getShippingFeeTemplates({ name: keyword });
      setTemplateOptions(response.templates || []);
    } catch (error) {
      console.error("获取模板列表失败:", error);
      setTemplateOptions([]);
    } finally {
      setTemplateLoading(false);
    }
  }, []);

  // 防抖的用户搜索
  const debouncedUserSearch = useCallback(
    debounce((keyword: string) => {
      loadUserOptions(keyword, 1, false);
    }, 300),
    [loadUserOptions]
  );

  // 防抖的模板搜索
  const debouncedTemplateSearch = useCallback(
    debounce((keyword: string) => {
      loadTemplateOptions(keyword);
    }, 300),
    [loadTemplateOptions]
  );

  // 初始化数据
  useEffect(() => {
    if (visible) {
      loadUserOptions("", 1, false);
      loadTemplateOptions();
    }
  }, [visible, loadUserOptions, loadTemplateOptions]);

  // 处理用户搜索
  const handleUserSearch = (value: string) => {
    debouncedUserSearch(value);
  };

  // 处理用户下拉框滚动，实现分页加载
  const handleUserPopupScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { target } = event;
    const { scrollTop, scrollHeight, clientHeight } = target as HTMLDivElement;

    // 当滚动到接近底部时加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 10 && !userLoading) {
      const hasMore = userOptions.length < userTotal;
      if (hasMore) {
        loadUserOptions("", userPage + 1, true);
      }
    }
  };

  // 处理模板搜索
  const handleTemplateSearch = (value: string) => {
    debouncedTemplateSearch(value);
  };

  // 处理计算模式切换
  const handleModeChange = (mode: CalculationMode) => {
    setCalculationMode(mode);
    setCalculationResult(null);
    form.resetFields();
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log("表单验证通过，提交数据:", values);
      setCalculating(true);

      if (calculationMode === "user") {
        // 根据用户计算运费
        const request: CalculateFreightByUserRequest = {
          userId: values.userId,
          cargoType: values.cargoType,
          weight: values.weight,
          length: values.length,
          width: values.width,
          height: values.height,
        };
        console.log("发送用户计算请求:", request);
        const result = await calculateFreightByUser(request);
        console.log("用户计算返回结果:", result);
        setCalculationResult(result);
      } else {
        // 根据模板计算运费
        const request: CalculateFreightByTemplateRequest = {
          templateId: values.templateId,
          weight: values.weight,
          length: values.length,
          width: values.width,
          height: values.height,
        };
        console.log("发送模板计算请求:", request);
        const result = await calculateFreightByTemplate(request);
        console.log("模板计算返回结果:", result);
        setCalculationResult(result);
      }

      message.success("运费计算成功");
    } catch (error) {
      console.error("运费计算失败:", error);

      // 更详细的错误处理
      let errorMessage = "运费计算失败，请检查参数后重试";
      if (error instanceof Error) {
        errorMessage = `计算失败: ${error.message}`;
      } else if (typeof error === "object" && error !== null) {
        errorMessage = `计算失败: ${JSON.stringify(error)}`;
      }

      message.error(errorMessage);

      // 重置计算结果，防止显示错误数据
      setCalculationResult(null);
    } finally {
      setCalculating(false);
    }
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    setCalculationResult(null);
  };

  // 处理关闭
  const handleClose = () => {
    form.resetFields();
    setCalculationResult(null);
    setCalculationMode("user");
    onClose();
  };

  // 生成图片并复制到剪贴板
  const handleCopyImageToClipboard = async () => {
    if (!resultCardRef.current || !calculationResult) {
      message.error("没有计算结果可以生成图片");
      return;
    }

    try {
      setGeneratingImage(true);

      // 使用html2canvas生成图片
      const canvas = await html2canvas(resultCardRef.current, {
        backgroundColor: "#ffffff",
        scale: 2, // 提高清晰度
        useCORS: true,
        allowTaint: true,
        width: resultCardRef.current.offsetWidth,
        height: resultCardRef.current.offsetHeight,
      } as Html2CanvasExtendedOptions);

      // 将canvas转换为blob
      canvas.toBlob(async (blob) => {
        if (!blob) {
          message.error("生成图片失败");
          return;
        }

        try {
          // 复制到剪贴板
          await navigator.clipboard.write([
            new ClipboardItem({ "image/png": blob }),
          ]);
          message.success(
            "运费计算结果图片已成功复制到剪贴板！可直接粘贴到其他应用中使用。"
          );
        } catch (clipboardError) {
          console.error("复制到剪贴板失败:", clipboardError);
          // 降级方案：下载图片
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `运费计算结果_${
            new Date().toISOString().split("T")[0]
          }.png`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
          message.warning(
            "无法复制到剪贴板，已自动下载图片到本地，请查看下载文件夹。"
          );
        }
      }, "image/png");
    } catch (error) {
      console.error("生成图片失败:", error);
      message.error("生成图片失败，请稍后重试");
    } finally {
      setGeneratingImage(false);
    }
  };

  // 下载图片
  const handleDownloadImage = async () => {
    if (!resultCardRef.current || !calculationResult) {
      message.error("没有计算结果可以生成图片");
      return;
    }

    try {
      setGeneratingImage(true);

      const canvas = await html2canvas(resultCardRef.current, {
        backgroundColor: "#ffffff",
        scale: 2,
        useCORS: true,
        allowTaint: true,
        width: resultCardRef.current.offsetWidth,
        height: resultCardRef.current.offsetHeight,
      } as Html2CanvasExtendedOptions);

      // 下载图片
      const url = canvas.toDataURL("image/png");
      const a = document.createElement("a");
      a.href = url;
      a.download = `运费计算结果_${new Date().toISOString().split("T")[0]}.png`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      message.success("运费计算结果图片下载成功！请查看浏览器下载文件夹。");
    } catch (error) {
      console.error("下载图片失败:", error);
      message.error("下载图片失败，请稍后重试");
    } finally {
      setGeneratingImage(false);
    }
  };

  // 渲染计算结果
  const renderCalculationResult = () => {
    if (!calculationResult) return null;

    const { template, calculationDetails, totalFreight } = calculationResult;

    // 安全检查，防止数据不完整导致的渲染错误
    if (!template || !calculationDetails || totalFreight === undefined) {
      return (
        <Card title="计算结果" style={{ marginTop: 16 }}>
          <Text type="secondary">计算结果数据不完整，请重试</Text>
        </Card>
      );
    }

    return (
      <Card
        title={
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <span>计算结果</span>
            <Space>
              <Button
                type="primary"
                icon={<CopyOutlined />}
                size="small"
                loading={generatingImage}
                onClick={handleCopyImageToClipboard}
              >
                复制图片
              </Button>
              <Button
                icon={<DownloadOutlined />}
                size="small"
                loading={generatingImage}
                onClick={handleDownloadImage}
              >
                下载图片
              </Button>
            </Space>
          </div>
        }
        size="small"
        style={{ height: "100%" }}
      >
        <div
          ref={resultCardRef}
          style={{ padding: "12px", backgroundColor: "#fff" }}
        >
          {/* 添加标题 */}
          <div style={{ textAlign: "center", marginBottom: 16 }}>
            <Typography.Title level={5} style={{ margin: 0, color: "#1890ff" }}>
              运费计算结果
            </Typography.Title>
          </div>

          <Row gutter={16}>
            <Col span={24}>
              {/* 基本信息 */}
              <Descriptions column={2} size="small" bordered>
                <Descriptions.Item label="总运费">
                  <Text strong style={{ color: "#1890ff", fontSize: "16px" }}>
                    ¥{(totalFreight || 0).toFixed(2)}
                  </Text>
                </Descriptions.Item>
                {"cargoType" in calculationResult && (
                  <Descriptions.Item label="货物类型">
                    {formatCargoType(calculationResult.cargoType)}
                  </Descriptions.Item>
                )}
              </Descriptions>

              <Divider orientation="left" orientationMargin="0">
                货物信息
              </Divider>

              <Descriptions column={4} size="small" bordered>
                <Descriptions.Item label="重量">
                  {calculationDetails.actualWeight?.toFixed(3) || 0}kg
                </Descriptions.Item>
                <Descriptions.Item label="长度">
                  {form.getFieldValue("length")?.toFixed(2) || 0}cm
                </Descriptions.Item>
                <Descriptions.Item label="宽度">
                  {form.getFieldValue("width")?.toFixed(2) || 0}cm
                </Descriptions.Item>
                <Descriptions.Item label="高度">
                  {form.getFieldValue("height")?.toFixed(2) || 0}cm
                </Descriptions.Item>
              </Descriptions>

              <Divider orientation="left" orientationMargin="0">
                计算详情
              </Divider>

              <Descriptions column={3} size="small">
                <Descriptions.Item label="实际重量">
                  {(calculationDetails.actualWeight || 0).toFixed(3)}kg
                </Descriptions.Item>
                <Descriptions.Item label="三边和">
                  {(calculationDetails.threeSidesSum || 0).toFixed(2)}cm
                </Descriptions.Item>
                <Descriptions.Item label="体积重量">
                  {(calculationDetails.dimensionalWeight || 0).toFixed(3)}kg
                </Descriptions.Item>
                <Descriptions.Item label="计费重量">
                  {(calculationDetails.chargeableWeight || 0).toFixed(3)}kg
                </Descriptions.Item>
                <Descriptions.Item label="首重费用">
                  ¥{(calculationDetails.firstWeightFee || 0).toFixed(2)}
                </Descriptions.Item>
                <Descriptions.Item label="续重费用">
                  ¥{(calculationDetails.continuedWeightFee || 0).toFixed(2)}
                </Descriptions.Item>
              </Descriptions>

              <Divider orientation="left" orientationMargin="0">
                模板信息
              </Divider>

              <Descriptions column={2} size="small">
                <Descriptions.Item label="首重价格">
                  ¥{template.firstWeightPrice || 0}
                </Descriptions.Item>
                <Descriptions.Item label="首重范围">
                  {template.firstWeightRange || 0}kg
                </Descriptions.Item>
                <Descriptions.Item label="续重价格">
                  ¥{template.continuedWeightPrice || 0}
                </Descriptions.Item>
                <Descriptions.Item label="续重区间">
                  {template.continuedWeightInterval || 0}kg
                </Descriptions.Item>
                <Descriptions.Item label="轻抛系数">
                  {template.bulkCoefficient || 0}
                </Descriptions.Item>
                <Descriptions.Item label="三边和起算点">
                  {template.threeSidesStart || 0}cm
                </Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </div>
      </Card>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <CalculatorOutlined />
          运费计算器
        </Space>
      }
      open={visible}
      onCancel={handleClose}
      width={1200}
      style={{ top: 20 }}
      footer={[
        <Button key="reset" onClick={handleReset}>
          重置
        </Button>,
        <Button key="cancel" onClick={handleClose}>
          关闭
        </Button>,
        <Button
          key="calculate"
          type="primary"
          loading={calculating}
          onClick={handleSubmit}
        >
          计算运费
        </Button>,
      ]}
      destroyOnClose
    >
      <Row gutter={16}>
        {/* 左侧：计算表单 */}
        <Col span={10}>
          {/* 计算模式选择 */}
          <Card size="small" style={{ marginBottom: 12 }}>
            <Radio.Group
              value={calculationMode}
              onChange={(e) => handleModeChange(e.target.value)}
              style={{ width: "100%" }}
            >
              <Row gutter={12}>
                <Col span={12}>
                  <Radio.Button
                    value="user"
                    style={{ width: "100%", textAlign: "center" }}
                  >
                    <UserOutlined /> 按用户计算
                  </Radio.Button>
                </Col>
                <Col span={12}>
                  <Radio.Button
                    value="template"
                    style={{ width: "100%", textAlign: "center" }}
                  >
                    <FileTextOutlined /> 按模板计算
                  </Radio.Button>
                </Col>
              </Row>
            </Radio.Group>
          </Card>

          {/* 计算表单 */}
          <Form
            form={form}
            layout="vertical"
            initialValues={{ cargoType: 1 }}
            size="small"
          >
            <Row gutter={12}>
              {calculationMode === "user" ? (
                <>
                  <Col span={24}>
                    <Form.Item
                      label="选择用户"
                      name="userId"
                      rules={[{ required: true, message: "请选择用户" }]}
                    >
                      <Select
                        showSearch
                        placeholder="输入用户名搜索"
                        filterOption={false}
                        onSearch={handleUserSearch}
                        onPopupScroll={handleUserPopupScroll}
                        loading={userLoading}
                        notFoundContent={
                          userLoading ? <Spin size="small" /> : <Empty />
                        }
                      >
                        {userOptions.map((user) => (
                          <Option key={user.id} value={user.id}>
                            {user.nickname || user.username} ({user.username})
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item
                      label="货物类型"
                      name="cargoType"
                      rules={[{ required: true, message: "请选择货物类型" }]}
                    >
                      <Radio.Group>
                        {CARGO_TYPE_OPTIONS.map((option) => (
                          <Radio key={option.value} value={option.value}>
                            {option.label}
                          </Radio>
                        ))}
                      </Radio.Group>
                    </Form.Item>
                  </Col>
                </>
              ) : (
                <Col span={24}>
                  <Form.Item
                    label="选择模板"
                    name="templateId"
                    rules={[{ required: true, message: "请选择运费模板" }]}
                  >
                    <Select
                      showSearch
                      placeholder="输入模板名称搜索"
                      filterOption={false}
                      onSearch={handleTemplateSearch}
                      loading={templateLoading}
                      notFoundContent={
                        templateLoading ? <Spin size="small" /> : <Empty />
                      }
                    >
                      {templateOptions.map((template) => (
                        <Option key={template.id} value={template.id}>
                          {template.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={12}>
              <Col span={12}>
                <Form.Item
                  label="重量 (kg)"
                  name="weight"
                  rules={[
                    { required: true, message: "请输入重量" },
                    { type: "number", min: 0.01, message: "重量必须大于0" },
                  ]}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="请输入重量"
                    min={0.01}
                    step={0.1}
                    precision={3}
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="长度 (cm)" name="length">
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="长度"
                    min={0}
                    step={0.01}
                    precision={2}
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={12}>
              <Col span={12}>
                <Form.Item label="宽度 (cm)" name="width">
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="宽度"
                    min={0}
                    step={0.01}
                    precision={2}
                    size="small"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="高度 (cm)" name="height">
                  <InputNumber
                    style={{ width: "100%" }}
                    placeholder="高度"
                    min={0}
                    step={0.01}
                    precision={2}
                    size="small"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Col>

        {/* 右侧：计算结果 */}
        <Col span={14}>
          {calculationResult ? (
            renderCalculationResult()
          ) : (
            <Card
              title="计算结果"
              size="small"
              style={{ height: "100%", minHeight: "400px" }}
            >
              <Empty
                description="请填写计算参数并点击「计算运费」按钮"
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "300px",
                }}
              />
            </Card>
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default FreightCalculatorModal;
