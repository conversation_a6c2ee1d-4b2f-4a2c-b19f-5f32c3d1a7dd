import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Tag,
  Space,
  Button,
  Form,
  Input,
  Row,
  Col,
  Select,
  DatePicker,
  App,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import type { TableColumnsType, TablePaginationConfig } from "antd";
import dayjs, { Dayjs } from "dayjs";
import {
  getProblemManifests,
  ProblemManifest,
  ProblemManifestQueryParams,
  PROBLEM_TYPE_OPTIONS,
} from "../../services/problemTicketService";
import { fetchUserOptions, UserOption } from "../../services/userService";
import CustomerSelector from "../../components/common/CustomerSelector";
import ManifestDetailDrawer from "../../components/manifest/ManifestDetailDrawer";

const { RangePicker } = DatePicker;

// 定义搜索表单值的类型
interface SearchFormValues {
  expressNumber?: string;
  userId?: number;
  shipmentTimeRange?: [Dayjs, Dayjs] | null;
  problemType?: number;
}

const ProblemTicketPage: React.FC = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<ProblemManifest[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [searchForm] = Form.useForm();

  // 详情抽屉相关状态
  const [detailDrawerVisible, setDetailDrawerVisible] = useState<boolean>(false);
  const [selectedManifestId, setSelectedManifestId] = useState<number | null>(null);

  // 加载数据
  const loadData = async (params: ProblemManifestQueryParams = {}) => {
    try {
      setLoading(true);
      const queryParams: ProblemManifestQueryParams = {
        page: params.page || current,
        pageSize: params.pageSize || pageSize,
        expressNumber: params.expressNumber || undefined,
        userId: params.userId || undefined,
        shipmentTimeStart: params.shipmentTimeStart || undefined,
        shipmentTimeEnd: params.shipmentTimeEnd || undefined,
        problemType: params.problemType || undefined,
      };

      // 清理undefined值
      Object.keys(queryParams).forEach(
        (key) =>
          queryParams[key as keyof ProblemManifestQueryParams] === undefined &&
          delete queryParams[key as keyof ProblemManifestQueryParams]
      );

      const result = await getProblemManifests(queryParams);
      setData(result.list || []);
      setTotal(result.total || 0);
    } catch (error) {
      console.error("加载问题运单列表失败:", error);
      message.error("加载问题运单列表失败");
      setData([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, [current, pageSize]);

  // 处理表格变化
  const handleTableChange = (pagination: TablePaginationConfig) => {
    if (pagination.current !== current) {
      setCurrent(pagination.current || 1);
    }
    if (pagination.pageSize !== pageSize) {
      setPageSize(pagination.pageSize || 10);
      setCurrent(1);
    }
  };

  // 处理搜索
  const handleSearch = (values: SearchFormValues) => {
    const searchParams: ProblemManifestQueryParams = {
      page: 1,
      pageSize,
      expressNumber: values.expressNumber,
      userId: values.userId,
      problemType: values.problemType,
    };

    // 处理时间范围
    if (values.shipmentTimeRange && values.shipmentTimeRange.length === 2) {
      searchParams.shipmentTimeStart = values.shipmentTimeRange[0].format("YYYY-MM-DD HH:mm:ss");
      searchParams.shipmentTimeEnd = values.shipmentTimeRange[1].format("YYYY-MM-DD HH:mm:ss");
    }

    setCurrent(1);
    loadData(searchParams);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setCurrent(1);
    loadData({ page: 1, pageSize });
  };

  // 查看详情
  const handleViewDetail = (manifestId: number) => {
    setSelectedManifestId(manifestId);
    setDetailDrawerVisible(true);
  };

  // 关闭详情抽屉
  const closeDetailDrawer = () => {
    setDetailDrawerVisible(false);
    setSelectedManifestId(null);
  };

  // 获取状态标签
  const getStatusTag = (status: number, statusName: string) => {
    const color = status === 16 ? "orange" : status === -1 ? "red" : "default";
    return <Tag color={color}>{statusName}</Tag>;
  };

  // 表格列定义
  const columns: TableColumnsType<ProblemManifest> = [
    {
      title: "序号",
      key: "index",
      width: 80,
      render: (_, __, index) => (current - 1) * pageSize + index + 1,
    },
    {
      title: "物流单号",
      dataIndex: "expressNumber",
      key: "expressNumber",
      width: 150,
    },
    {
      title: "客户",
      dataIndex: "userNickname",
      key: "userNickname",
      width: 120,
    },
    {
      title: "追踪状态",
      key: "trackingStatus",
      width: 180,
      render: (_, record) => getStatusTag(record.trackingStatus, record.trackingStatusName),
    },
    {
      title: "发货时间",
      dataIndex: "shipmentTime",
      key: "shipmentTime",
      width: 150,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 150,
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record.id)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Card title="问题运单管理" bordered={false}>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="horizontal"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="物流单号" name="expressNumber">
                <Input placeholder="请输入物流单号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="客户" name="userId">
                <CustomerSelector placeholder="请选择客户" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item label="问题类型" name="problemType">
                <Select
                  placeholder="选择问题类型"
                  options={PROBLEM_TYPE_OPTIONS}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={24} lg={10}>
              <Form.Item label="发货时间" name="shipmentTimeRange">
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col xs={24} style={{ textAlign: "right" }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                >
                  搜索
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (totalCount) => `共 ${totalCount} 条记录`,
            responsive: true,
          }}
          onChange={handleTableChange}
          loading={loading}
          scroll={{ x: "max-content" }}
          bordered
          size="middle"
        />
      </Card>

      {/* 详情抽屉组件 */}
      <ManifestDetailDrawer
        manifestId={selectedManifestId}
        visible={detailDrawerVisible}
        onClose={closeDetailDrawer}
      />
    </>
  );
};

export default ProblemTicketPage;
