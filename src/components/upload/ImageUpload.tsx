import React, { useState } from "react";
import {
  Upload,
  Button,
  message,
  Image,
  Card,
  Space,
  Typography,
  Popconfirm,
  Tooltip,
  Progress,
} from "antd";
import {
  UploadOutlined,
  DeleteOutlined,
  EyeOutlined,
  InboxOutlined,
} from "@ant-design/icons";
import {
  uploadImage,
  deleteImage,
  validateImageFile,
  BusinessType,
  formatFileSize,
  extractFileNameFromUrl,
  type ImageUploadData,
} from "../../services/uploadService";

const { Text } = Typography;

// 上传文件状态接口
interface UploadedFile {
  uid: string;
  name: string;
  url: string;
  size: number;
  uploadTime: string;
}

// 组件属性接口
interface ImageUploadProps {
  value?: string[]; // 当前的图片URL数组
  onChange?: (urls: string[]) => void; // URL变化回调
  businessType: BusinessType; // 业务类型
  maxCount?: number; // 最大上传数量，默认为1
  disabled?: boolean; // 是否禁用
  placeholder?: string; // 占位符文本
  dragUpload?: boolean; // 是否启用拖拽上传，默认为false
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  value = [],
  onChange,
  businessType,
  maxCount = 1,
  disabled = false,
  placeholder = "点击上传图片",
  dragUpload = false,
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>(() => {
    // 初始化已上传文件列表
    return value.map((url, index) => ({
      uid: `initial-${index}`,
      name: extractFileNameFromUrl(url),
      url,
      size: 0, // 初始文件大小未知
      uploadTime: "", // 初始上传时间未知
    }));
  });

  /**
   * 处理文件上传前的验证
   */
  const beforeUpload = (file: File): boolean => {
    // 检查数量限制
    if (uploadedFiles.length >= maxCount) {
      message.error(`最多只能上传${maxCount}张图片`);
      return false;
    }

    // 验证文件
    const error = validateImageFile(file);
    if (error) {
      message.error(error);
      return false;
    }

    // 开始上传
    handleUpload(file);
    return false; // 阻止自动上传，使用自定义上传逻辑
  };

  /**
   * 处理拖拽事件
   */
  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled || uploading) {
      return;
    }

    // 检查数量限制
    if (uploadedFiles.length >= maxCount) {
      message.error(`最多只能上传${maxCount}张图片`);
      return;
    }

    const files = Array.from(e.dataTransfer.files);
    const items = Array.from(e.dataTransfer.items);

    console.log("拖拽数据:", { files: files.length, items: items.length });
    console.log("所有DataTransfer类型:", e.dataTransfer.types);

    // 详细记录每个item的信息
    items.forEach((item, index) => {
      console.log(`Item ${index}:`, {
        type: item.type,
        kind: item.kind,
      });
    });

    // 首先尝试处理标准文件
    for (const file of files) {
      if (file.type.startsWith("image/")) {
        console.log("处理标准图片文件:", file.name, file.type);
        const error = validateImageFile(file);
        if (error) {
          message.error(error);
          continue;
        }
        await handleUpload(file);
        return; // 一次只处理一个文件
      }
    }

    // 处理DataTransferItem（支持从浏览器、聊天软件等拖拽）
    for (const item of items) {
      console.log("处理DataTransferItem:", item.type, item.kind);

      // 处理图片文件
      if (item.kind === "file" && item.type.startsWith("image/")) {
        const file = item.getAsFile();
        if (file) {
          console.log("从DataTransferItem获取文件:", file.name, file.type);

          // 如果文件名为空或者是blob，给它一个有意义的名称
          let finalFile = file;
          if (!file.name || file.name === "blob" || file.name === "") {
            const extension = file.type.split("/")[1] || "png";
            const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
            finalFile = new File([file], `image-${timestamp}.${extension}`, {
              type: file.type,
            });
          }

          const error = validateImageFile(finalFile);
          if (error) {
            message.error(error);
            continue;
          }
          await handleUpload(finalFile);
          return; // 一次只处理一个文件
        }
      }

      // 处理文本类型（可能包含图片URL或base64数据）
      if (
        item.kind === "string" &&
        (item.type === "text/plain" || item.type === "text/html")
      ) {
        console.log("处理文本类型数据:", item.type);

        item.getAsString(async (textData) => {
          console.log("获取到文本数据:", textData.substring(0, 200) + "...");
          console.log("完整文本数据:", textData);

          // 检查是否是图片URL（支持更多URL格式）
          const imageUrlRegex =
            /https?:\/\/[^\s\n\r<>"']+\.(jpg|jpeg|png|gif|bmp|webp|avif|svg)(\?[^\s\n\r<>"']*)?/i;
          const urlMatch = textData.match(imageUrlRegex);

          if (urlMatch) {
            console.log("检测到图片URL:", urlMatch[0]);
            try {
              await downloadImageFromUrl(urlMatch[0]);
              return;
            } catch (error) {
              console.error("从URL下载图片失败:", error);
            }
          }

          // 检查是否是base64图片数据
          const base64Regex = /data:image\/[a-zA-Z]*;base64,([^"]*)/;
          const base64Match = textData.match(base64Regex);

          if (base64Match) {
            console.log("检测到base64图片数据");
            try {
              await handleBase64Image(base64Match[0]);
              return;
            } catch (error) {
              console.error("处理base64图片失败:", error);
            }
          }

          // 检查是否包含可能的图片URL（没有扩展名的情况）
          const generalUrlRegex = /https?:\/\/[^\s\n\r<>"']+/g;
          const urls = textData.match(generalUrlRegex);

          if (urls && urls.length > 0) {
            console.log("检测到可能的图片URL:", urls);
            for (const url of urls) {
              try {
                // 尝试通过HEAD请求检查是否是图片
                const response = await fetch(url, {
                  method: "HEAD",
                  mode: "cors",
                });
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.startsWith("image/")) {
                  console.log("确认是图片URL:", url);
                  await downloadImageFromUrl(url);
                  return;
                }
              } catch (error) {
                console.log("URL检查失败:", url, error);
              }
            }
          }

          console.log("文本数据不包含有效的图片信息");

          // 对于聊天软件的占位符文本，尝试其他方法
          if (
            textData.includes("[图片]") ||
            textData.includes("图片") ||
            textData.includes("image")
          ) {
            console.log("检测到可能是聊天软件的图片占位符");
            message.warning(
              "检测到图片占位符，但无法获取实际图片数据。请尝试先保存图片到本地，然后拖拽图片文件。"
            );
          } else {
            message.warning(
              "拖拽的内容不包含有效的图片信息，请直接拖拽图片文件"
            );
          }
        });

        return; // 异步处理，直接返回
      }

      // 尝试处理其他可能的数据类型
      if (
        item.kind === "string" &&
        item.type !== "text/plain" &&
        item.type !== "text/html"
      ) {
        console.log("尝试处理其他字符串类型:", item.type);
        item.getAsString((data) => {
          console.log(`${item.type} 数据:`, data);
        });
      }
    }

    // 特殊处理：尝试从dataTransfer中查找其他可能的图片数据
    console.log("检查DataTransfer.types:", e.dataTransfer.types);

    // 尝试处理可能存在的二进制数据或特殊格式
    for (const type of e.dataTransfer.types) {
      if (type.startsWith("image/") || type.includes("image")) {
        console.log("发现可能的图片类型:", type);
        try {
          const data = e.dataTransfer.getData(type);
          console.log(`从 ${type} 获取的数据:`, data.substring(0, 100) + "...");

          if (data.startsWith("data:image/")) {
            await handleBase64Image(data);
            return;
          }
        } catch (error) {
          console.log(`无法从 ${type} 获取数据:`, error);
        }
      }
    }

    // 如果没有找到有效的图片文件
    if (files.length === 0 && items.length === 0) {
      message.error("请拖拽图片文件到此区域");
    } else {
      console.log(
        "未找到有效图片，files:",
        files.map((f) => ({ name: f.name, type: f.type }))
      );
      console.log(
        "未找到有效图片，items:",
        items.map((i) => ({ type: i.type, kind: i.kind }))
      );

      // 检查是否是聊天软件的图片拖拽
      const hasImagePlaceholder = items.some(
        (item) => item.type === "text/plain" && item.kind === "string"
      );

      if (hasImagePlaceholder) {
        message.warning(
          "检测到可能来自聊天软件的图片拖拽，但无法直接获取图片数据。建议：\n1. 右键图片选择 '复制图片' 然后粘贴（Ctrl+V）\n2. 或者先保存图片到本地再拖拽文件"
        );
      } else {
        message.error("未找到有效的图片文件，请确保拖拽的是图片格式");
      }
    }
  };

  /**
   * 处理拖拽悬停
   */
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  /**
   * 处理拖拽进入
   */
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  /**
   * 处理拖拽离开
   */
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  /**
   * 处理粘贴事件（支持截图粘贴）
   */
  const handlePaste = async (e: React.ClipboardEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 检查数量限制
    if (uploadedFiles.length >= maxCount) {
      message.error(`最多只能上传${maxCount}张图片`);
      return;
    }

    const items = Array.from(e.clipboardData.items);

    for (const item of items) {
      if (item.type.startsWith("image/")) {
        const file = item.getAsFile();
        if (file) {
          // 为截图文件生成一个名称
          const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
          const newFile = new File([file], `screenshot-${timestamp}.png`, {
            type: file.type,
          });

          const error = validateImageFile(newFile);
          if (error) {
            message.error(error);
            continue;
          }

          await handleUpload(newFile);
          message.success("截图粘贴成功");
          return;
        }
      }
    }

    message.error("剪贴板中没有找到图片，请先截图或复制图片");
  };

  /**
   * 从URL下载图片并上传
   */
  const downloadImageFromUrl = async (imageUrl: string) => {
    try {
      console.log("开始从URL下载图片:", imageUrl);

      // 使用fetch下载图片
      const response = await fetch(imageUrl, {
        mode: "cors",
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();

      // 检查是否是图片类型
      if (!blob.type.startsWith("image/")) {
        throw new Error("下载的文件不是图片格式");
      }

      // 从URL中提取文件名
      const urlParts = imageUrl.split("/");
      const urlFileName = urlParts[urlParts.length - 1].split("?")[0];
      const extension = blob.type.split("/")[1] || "png";
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const fileName =
        urlFileName || `downloaded-image-${timestamp}.${extension}`;

      // 创建File对象
      const file = new File([blob], fileName, { type: blob.type });

      const error = validateImageFile(file);
      if (error) {
        message.error(error);
        return;
      }

      await handleUpload(file);
      message.success("图片下载并上传成功");
    } catch (error) {
      console.error("从URL下载图片失败:", error);
      message.error(
        `下载图片失败: ${error instanceof Error ? error.message : "未知错误"}`
      );
    }
  };

  /**
   * 处理base64图片数据
   */
  const handleBase64Image = async (base64Data: string) => {
    try {
      console.log("开始处理base64图片数据");

      // 提取base64数据和mime类型
      const matches = base64Data.match(/^data:([^;]+);base64,(.+)$/);
      if (!matches) {
        throw new Error("无效的base64图片数据格式");
      }

      const mimeType = matches[1];
      const base64Content = matches[2];

      // 检查是否是图片类型
      if (!mimeType.startsWith("image/")) {
        throw new Error("base64数据不是图片格式");
      }

      // 将base64转换为blob
      const byteCharacters = atob(base64Content);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: mimeType });

      // 生成文件名
      const extension = mimeType.split("/")[1] || "png";
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const fileName = `base64-image-${timestamp}.${extension}`;

      // 创建File对象
      const file = new File([blob], fileName, { type: mimeType });

      const error = validateImageFile(file);
      if (error) {
        message.error(error);
        return;
      }

      await handleUpload(file);
      message.success("base64图片处理并上传成功");
    } catch (error) {
      console.error("处理base64图片失败:", error);
      message.error(
        `处理base64图片失败: ${
          error instanceof Error ? error.message : "未知错误"
        }`
      );
    }
  };

  /**
   * 处理文件上传
   */
  const handleUpload = async (file: File) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 100);

      // 调用上传接口
      const uploadResult: ImageUploadData = await uploadImage(
        file,
        businessType
      );

      // 清除进度定时器
      clearInterval(progressInterval);
      setUploadProgress(100);

      // 创建新的文件记录
      const newFile: UploadedFile = {
        uid: `upload-${Date.now()}`,
        name: file.name,
        url: uploadResult.fileUrl,
        size: uploadResult.fileSize,
        uploadTime: uploadResult.uploadTime,
      };

      // 更新文件列表
      const newFileList = [...uploadedFiles, newFile];
      setUploadedFiles(newFileList);

      // 通知外部组件URL变化
      const newUrls = newFileList.map((f) => f.url);
      onChange?.(newUrls);

      message.success("图片上传成功");
    } catch (error) {
      console.error("图片上传失败:", error);
      message.error("图片上传失败，请重试");
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  /**
   * 处理文件删除
   */
  const handleDelete = async (file: UploadedFile) => {
    try {
      // 调用删除接口（如果是新上传的文件）
      if (!file.uid.startsWith("initial-")) {
        await deleteImage(file.url);
      }

      // 更新文件列表
      const newFileList = uploadedFiles.filter((f) => f.uid !== file.uid);
      setUploadedFiles(newFileList);

      // 通知外部组件URL变化
      const newUrls = newFileList.map((f) => f.url);
      onChange?.(newUrls);

      message.success("图片删除成功");
    } catch (error) {
      console.error("图片删除失败:", error);
      message.error("图片删除失败，请重试");
    }
  };

  /**
   * 渲染上传按钮
   */
  const renderUploadButton = () => {
    if (uploadedFiles.length >= maxCount) {
      return null;
    }

    // 如果启用拖拽上传，使用拖拽区域
    if (dragUpload) {
      const { Dragger } = Upload;
      return (
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onPaste={handlePaste}
          tabIndex={0}
          style={{ outline: "none" }}
        >
          <Dragger
            beforeUpload={beforeUpload}
            showUploadList={false}
            disabled={disabled || uploading}
            accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
            style={{
              background: "#fafafa",
              border: "1px dashed #d9d9d9",
              borderRadius: "6px",
              cursor: disabled || uploading ? "not-allowed" : "pointer",
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined style={{ fontSize: "48px", color: "#d9d9d9" }} />
            </p>
            <p
              className="ant-upload-text"
              style={{ fontSize: "16px", margin: "0 0 4px 0" }}
            >
              {uploading
                ? `上传中 ${uploadProgress}%`
                : "点击或拖拽图片到此区域上传"}
            </p>
            <p
              className="ant-upload-hint"
              style={{ fontSize: "14px", color: "#666" }}
            >
              {placeholder}
              <br />
              <span style={{ fontSize: "12px", color: "#999" }}>
                支持截图粘贴（Ctrl+V）、复制图片粘贴、本地文件拖拽
                <br />
                聊天软件图片请右键复制后粘贴，或保存到本地后拖拽
              </span>
            </p>
          </Dragger>
        </div>
      );
    }

    // 普通按钮上传
    return (
      <Upload
        beforeUpload={beforeUpload}
        showUploadList={false}
        disabled={disabled || uploading}
        accept=".jpg,.jpeg,.png,.gif,.bmp,.webp"
      >
        <Button
          icon={<UploadOutlined />}
          loading={uploading}
          disabled={disabled}
          style={{ width: "100%" }}
        >
          {uploading ? `上传中 ${uploadProgress}%` : placeholder}
        </Button>
      </Upload>
    );
  };

  /**
   * 渲染文件列表
   */
  const renderFileList = () => {
    if (uploadedFiles.length === 0) {
      return null;
    }

    return (
      <div style={{ marginTop: "12px" }}>
        <Space direction="vertical" style={{ width: "100%" }}>
          {uploadedFiles.map((file) => (
            <Card
              key={file.uid}
              size="small"
              style={{ padding: "8px" }}
              bodyStyle={{ padding: "8px" }}
            >
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Space>
                  {/* 直接显示图片缩略图 */}
                  <div
                    style={{
                      width: "40px",
                      height: "40px",
                      border: "1px solid #d9d9d9",
                      borderRadius: "4px",
                      overflow: "hidden",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      cursor: "pointer",
                    }}
                  >
                    <Image
                      src={file.url}
                      alt={file.name}
                      style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                      }}
                      preview={{
                        mask: <EyeOutlined style={{ color: "#fff" }} />,
                      }}
                      fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAxNkMyMS4xMDQ2IDE2IDIyIDE2Ljg5NTQgMjIgMThDMjIgMTkuMTA0NiAyMS4xMDQ2IDIwIDIwIDIwQzE4Ljg5NTQgMjAgMTggMTkuMTA0NiAxOCAxOEMxOCAxNi44OTU0IDE4Ljg5NTQgMTYgMjAgMTZaIiBmaWxsPSIjQkZCRkJGIi8+CjxwYXRoIGQ9Ik0xNCAyNEwyMCAyMEwyNiAyNEgxNFoiIGZpbGw9IiNCRkJGQkYiLz4KPHN2Zz4K"
                    />
                  </div>
                  <div>
                    <Text
                      strong
                      style={{
                        fontSize: "14px",
                        maxWidth: "200px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        display: "inline-block",
                      }}
                    >
                      {file.name}
                    </Text>
                    {file.size > 0 && (
                      <div>
                        <Text type="secondary" style={{ fontSize: "12px" }}>
                          {formatFileSize(file.size)}
                          {file.uploadTime && ` · ${file.uploadTime}`}
                        </Text>
                      </div>
                    )}
                  </div>
                </Space>
                <Space>
                  <Popconfirm
                    title="确认删除"
                    description="确定要删除这张图片吗？"
                    onConfirm={() => handleDelete(file)}
                    okText="删除"
                    cancelText="取消"
                    disabled={disabled}
                  >
                    <Tooltip title="删除图片">
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        danger
                        disabled={disabled}
                      />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              </div>
            </Card>
          ))}
        </Space>
      </div>
    );
  };

  return (
    <div>
      {/* 上传进度条 */}
      {uploading && (
        <Progress
          percent={uploadProgress}
          size="small"
          style={{ marginBottom: "12px" }}
          status="active"
        />
      )}

      {/* 上传按钮 */}
      {renderUploadButton()}

      {/* 文件列表 */}
      {renderFileList()}

      {/* 提示信息 */}
      <div style={{ marginTop: "8px" }}>
        <Text type="secondary" style={{ fontSize: "12px" }}>
          支持jpg、png等格式，单个文件最大10MB
          {maxCount > 1 && `，最多上传${maxCount}张图片`}
          {uploadedFiles.length > 0 && " · 点击图片可放大预览"}
        </Text>
      </div>
    </div>
  );
};

export default ImageUpload;
