import React, { useState, useEffect } from "react";
import {
  Modal,
  Radio,
  Card,
  Descriptions,
  Typography,
  Space,
  Button,
  App,
  Spin,
  Divider,
  Row,
  Col,
} from "antd";
import {
  EditOutlined,
  CalculatorOutlined,
  CheckOutlined,
} from "@ant-design/icons";
import {
  calculateFreightByManifest,
  updateManifestTemplateType,
  CalculateFreightByManifestData,
  UpdateManifestTemplateTypeData,
  CARGO_TYPE_OPTIONS,
} from "../../services/manifestFreightService";

const { Text } = Typography;

interface ManifestTemplateModalProps {
  visible: boolean;
  manifestId: number | null;
  onClose: () => void;
  onSuccess: (result: UpdateManifestTemplateTypeData) => void;
}

const ManifestTemplateModal: React.FC<ManifestTemplateModalProps> = ({
  visible,
  manifestId,
  onClose,
  onSuccess,
}) => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState<boolean>(false);
  const [calculating, setCalculating] = useState<boolean>(false);
  const [updating, setUpdating] = useState<boolean>(false);
  const [calculationResult, setCalculationResult] =
    useState<CalculateFreightByManifestData | null>(null);
  const [selectedCargoType, setSelectedCargoType] = useState<number | null>(
    null
  );
  const [originalCargoType, setOriginalCargoType] = useState<number | null>(
    null
  );

  // 初始加载运单信息并计算运费
  useEffect(() => {
    if (visible && manifestId) {
      loadInitialData();
    }
  }, [visible, manifestId]);

  // 重置状态
  useEffect(() => {
    if (!visible) {
      setCalculationResult(null);
      setSelectedCargoType(null);
      setOriginalCargoType(null);
      setLoading(false);
      setCalculating(false);
      setUpdating(false);
    }
  }, [visible]);

  // 加载初始数据
  const loadInitialData = async () => {
    if (!manifestId) return;

    try {
      setLoading(true);
      const result = await calculateFreightByManifest({ manifestId });
      setCalculationResult(result);
      setSelectedCargoType(result.usedCargoType);
      setOriginalCargoType(result.usedCargoType);
    } catch (error) {
      console.error("加载运单运费信息失败:", error);
      message.error("加载运单运费信息失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 处理货物类型变化
  const handleCargoTypeChange = async (cargoType: number) => {
    if (!manifestId || cargoType === selectedCargoType) return;

    try {
      setCalculating(true);
      setSelectedCargoType(cargoType);
      const result = await calculateFreightByManifest({
        manifestId,
        cargoType,
      });
      setCalculationResult(result);
    } catch (error) {
      console.error("重新计算运费失败:", error);
      message.error("重新计算运费失败，请稍后重试");
      // 回滚选择
      setSelectedCargoType(selectedCargoType);
    } finally {
      setCalculating(false);
    }
  };

  // 处理确认修改
  const handleConfirmUpdate = async () => {
    if (!manifestId || !selectedCargoType) return;

    try {
      setUpdating(true);
      const result = await updateManifestTemplateType({
        id: manifestId,
        templateType: selectedCargoType,
      });
      message.success("修改运单模板成功");
      onSuccess(result);
      onClose();
    } catch (error) {
      console.error("修改运单模板失败:", error);
      message.error("修改运单模板失败，请稍后重试");
    } finally {
      setUpdating(false);
    }
  };

  // 渲染计算结果
  const renderCalculationResult = () => {
    if (!calculationResult) return null;

    const { template, calculationDetails, totalFreight } = calculationResult;

    return (
      <div style={{ marginTop: 16 }}>
        {/* 基本信息 */}
        <Descriptions column={2} size="small" bordered>
          <Descriptions.Item label="运单号">
            {calculationResult.expressNumber}
          </Descriptions.Item>
          <Descriptions.Item label="总运费">
            <Text strong style={{ color: "#1890ff", fontSize: "16px" }}>
              ¥{totalFreight.toFixed(2)}
            </Text>
          </Descriptions.Item>
        </Descriptions>

        <Divider orientation="left" orientationMargin="0">
          货物信息
        </Divider>

        <Descriptions column={4} size="small" bordered>
          <Descriptions.Item label="重量">
            {calculationDetails.actualWeight.toFixed(3)}kg
          </Descriptions.Item>
          <Descriptions.Item label="长度">
            {calculationDetails.length.toFixed(2)}cm
          </Descriptions.Item>
          <Descriptions.Item label="宽度">
            {calculationDetails.width.toFixed(2)}cm
          </Descriptions.Item>
          <Descriptions.Item label="高度">
            {calculationDetails.height.toFixed(2)}cm
          </Descriptions.Item>
        </Descriptions>

        <Divider orientation="left" orientationMargin="0">
          计算详情
        </Divider>

        <Descriptions column={3} size="small" bordered>
          <Descriptions.Item label="实际重量">
            {calculationDetails.actualWeight.toFixed(3)}kg
          </Descriptions.Item>
          <Descriptions.Item label="三边和">
            {calculationDetails.threeSidesSum.toFixed(2)}cm
          </Descriptions.Item>
          <Descriptions.Item label="体积重量">
            {calculationDetails.dimensionalWeight.toFixed(3)}kg
          </Descriptions.Item>
          <Descriptions.Item label="计费重量">
            {calculationDetails.chargeableWeight.toFixed(3)}kg
          </Descriptions.Item>
          <Descriptions.Item label="首重费用">
            ¥{calculationDetails.firstWeightFee.toFixed(2)}
          </Descriptions.Item>
          <Descriptions.Item label="续重费用">
            ¥{calculationDetails.continuedWeightFee.toFixed(2)}
          </Descriptions.Item>
        </Descriptions>

        <Divider orientation="left" orientationMargin="0">
          模板信息
        </Divider>

        <Descriptions column={2} size="small" bordered>
          <Descriptions.Item label="模板名称">
            {template.name}
          </Descriptions.Item>
          <Descriptions.Item label="首重价格">
            ¥{template.firstWeightPrice}
          </Descriptions.Item>
          <Descriptions.Item label="首重范围">
            {template.firstWeightRange}kg
          </Descriptions.Item>
          <Descriptions.Item label="续重价格">
            ¥{template.continuedWeightPrice}
          </Descriptions.Item>
          <Descriptions.Item label="续重区间">
            {template.continuedWeightInterval}kg
          </Descriptions.Item>
          <Descriptions.Item label="轻抛系数">
            {template.bulkCoefficient}
          </Descriptions.Item>
          <Descriptions.Item label="三边和起算点">
            {template.threeSidesStart}cm
          </Descriptions.Item>
        </Descriptions>
      </div>
    );
  };

  // 判断是否有变化
  const hasChanges = selectedCargoType !== originalCargoType;

  return (
    <Modal
      title={
        <Space>
          <EditOutlined />
          修改运单模板
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={900}
      style={{ top: 20 }}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          icon={<CheckOutlined />}
          loading={updating}
          disabled={!hasChanges || calculating}
          onClick={handleConfirmUpdate}
        >
          确认修改
        </Button>,
      ]}
      destroyOnClose
    >
      <Spin spinning={loading} tip="加载运单信息中...">
        {/* 货物类型选择 */}
        <Card title="选择货物类型" size="small" style={{ marginBottom: 16 }}>
          <Radio.Group
            value={selectedCargoType}
            onChange={(e) => handleCargoTypeChange(e.target.value)}
            disabled={calculating}
          >
            <Row gutter={16}>
              {CARGO_TYPE_OPTIONS.map((option) => (
                <Col span={6} key={option.value}>
                  <Radio value={option.value} style={{ marginBottom: 8 }}>
                    {option.label}
                  </Radio>
                </Col>
              ))}
            </Row>
          </Radio.Group>
          {calculating && (
            <div style={{ textAlign: "center", marginTop: 16 }}>
              <Spin size="small" />
              <Text style={{ marginLeft: 8 }}>重新计算运费中...</Text>
            </div>
          )}
        </Card>

        {/* 计算结果 */}
        <Card
          title={
            <Space>
              <CalculatorOutlined />
              运费计算结果
              {hasChanges && (
                <Text type="warning" style={{ fontSize: "12px" }}>
                  (已修改，请确认后保存)
                </Text>
              )}
            </Space>
          }
          size="small"
        >
          {calculationResult ? (
            renderCalculationResult()
          ) : (
            <div style={{ textAlign: "center", padding: "40px 0" }}>
              <Spin size="large" />
              <Text style={{ display: "block", marginTop: 16 }}>
                正在加载运费信息...
              </Text>
            </div>
          )}
        </Card>
      </Spin>
    </Modal>
  );
};

export default ManifestTemplateModal;
