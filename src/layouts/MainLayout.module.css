/* src/layouts/MainLayout.module.css */
.mainLayout {
  min-height: 100vh;
}

.logoContainer {
  height: 64px; /* 与 Header 高度一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px; /* 添加左右内边距，避免 logo 贴着边缘 */
  overflow: hidden; /* 防止文字溢出 */
  background: rgba(0, 0, 0, 0.2); /* 给 logo 区域加点背景，使 logo 更加突出 */
}

.logoIcon {
  height: 32px; /* 设置 logo 高度 */
  width: auto; /* 保持宽高比 */
  transition: all 0.3s;
}

.logoText {
  color: #fff;
  margin-left: 12px;
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap; /* 防止文字换行 */
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
    width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* Sider 收起时隐藏文字 */
:global(.ant-layout-sider-collapsed) .logoText {
  opacity: 0;
  width: 0;
  margin-left: 0;
}

.siteLayout {
  background: #f0f2f5; /* 内容区域背景色 */
}

.siteHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px !important; /* 覆盖 antd 默认 padding */
  background: #fff !important;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 64px;
}

.headerLeft {
  display: flex;
  align-items: center;
}

.headerRight {
  display: flex;
  align-items: center;
}

.trigger {
  padding: 0 10px;
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.actionIcon {
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
  color: rgba(0, 0, 0, 0.65);
}
.actionIcon:hover {
  color: #1890ff;
}

/* 运费计算按钮样式 */
.actionIcon.ant-btn {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  border: none;
  background: transparent;
  font-size: 14px;
}
.actionIcon.ant-btn:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.userInfo {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.userInfo span {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.85);
}

.siteContent {
  margin: 24px 16px;
  padding: 24px; /* 给内容区域加内边距 */
  background: #fff; /* 内容区域白色背景 */
  min-height: 280px;
  border-radius: 8px; /* 内容区域圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); /* 内容区域阴影 */
  /* 如果内容可能超出，允许滚动 */
  overflow: auto;
}
