import { apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 账单状态枚举
export enum BillStatus {
  UNPAID = "UNPAID", // 未付款
  PAID = "PAID", // 已付款
  PARTIAL_PAID = "PARTIAL_PAID", // 部分付款
  OVERDUE = "OVERDUE", // 逾期
  CANCELLED = "CANCELLED", // 已取消
  ERROR = "ERROR", // 异常
}

// 账单记录接口
export interface BillingRecord {
  id: number; // 账单主键ID
  billNumber: string; // 账单编号
  customerAccountId: number; // 客户账户ID
  customerNickname: string; // 客户昵称
  billDate: string; // 账单日期（yyyy-MM-dd 格式）
  dueDate?: string; // 付款截止日期（yyyy-MM-dd 格式），可为 null
  billingPeriodStart: string; // 账期开始日期（yyyy-MM-dd 格式）
  billingPeriodEnd: string; // 账期结束日期（yyyy-MM-dd 格式）
  totalAmount: number; // 账单总金额
  amountPaid: number; // 已付金额
  balanceDue: number; // 应付余额
  currency: string; // 货币单位
  status: BillStatus; // 账单状态
  paymentMethod?: string; // 支付方式，可为 null
  paymentTransactionId?: string; // 支付交易号，可为 null
  paymentDate?: string; // 支付日期（yyyy-MM-dd HH:mm:ss 格式），可为 null
  notes?: string; // 账单备注，可为 null
  generatedByUserId?: number; // 账单生成操作员ID，可为 null
  generatedByNickname?: string; // 账单生成操作员昵称，可为 null
  createTime: string; // 记录创建时间（yyyy-MM-dd HH:mm:ss 格式）
  updateTime: string; // 记录更新时间（yyyy-MM-dd HH:mm:ss 格式）
}

// 账单查询参数接口
export interface BillingRecordQueryParams {
  page?: number; // 页码，默认1
  pageSize?: number; // 每页数量，默认10，最大100
  billNumber?: string; // 账单编号
  customerAccountId?: number; // 客户账户ID
  status?: BillStatus; // 账单状态
  currency?: string; // 货币单位
  minAmount?: number; // 最小金额
  maxAmount?: number; // 最大金额
  billDateStart?: string; // 账单日期开始（yyyy-MM-dd 格式）
  billDateEnd?: string; // 账单日期结束（yyyy-MM-dd 格式）
  billingPeriodStart?: string; // 账期开始日期（yyyy-MM-dd 格式）
  billingPeriodEnd?: string; // 账期结束日期（yyyy-MM-dd 格式）
  billingCycleId?: number; // 账期批次ID
}

// 账单分页数据接口
export interface BillingRecordPageData {
  total: number; // 总记录数
  list: BillingRecord[]; // 账单记录列表
}

// API响应接口
export interface BillingRecordResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: BillingRecordPageData;
}

/**
 * 分页查询账单记录列表
 * @param params 查询参数
 * @returns 账单记录分页数据
 */
export const fetchBillingRecords = async (
  params: BillingRecordQueryParams = {}
): Promise<BillingRecordPageData> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的try-catch
  const response = await apiClient.get<BillingRecordResponse>(
    getApiPath("/finance/billing/records"),
    { params }
  );

  return response.data.data;
};

// 账单状态选项，用于筛选下拉框
export const BILL_STATUS_OPTIONS = [
  { value: BillStatus.UNPAID, label: "未付款" },
  { value: BillStatus.PAID, label: "已付款" },
  { value: BillStatus.PARTIAL_PAID, label: "部分付款" },
  { value: BillStatus.OVERDUE, label: "逾期" },
  { value: BillStatus.CANCELLED, label: "已取消" },
  { value: BillStatus.ERROR, label: "异常" },
];

// 货币选项
export const CURRENCY_OPTIONS = [
  { value: "CNY", label: "人民币 (CNY)" },
  { value: "USD", label: "美元 (USD)" },
  { value: "EUR", label: "欧元 (EUR)" },
  { value: "GBP", label: "英镑 (GBP)" },
  { value: "JPY", label: "日元 (JPY)" },
];

/**
 * 格式化账单状态为中文显示
 * @param status 账单状态
 * @returns 中文状态描述
 */
export const formatBillStatus = (status: BillStatus): string => {
  const statusMap = {
    [BillStatus.UNPAID]: "未付款",
    [BillStatus.PAID]: "已付款",
    [BillStatus.PARTIAL_PAID]: "部分付款",
    [BillStatus.OVERDUE]: "逾期",
    [BillStatus.CANCELLED]: "已取消",
    [BillStatus.ERROR]: "异常",
  };
  return statusMap[status] || "未知状态";
};

// 运费模板类型枚举
export enum ShippingTemplateType {
  GENERAL = 1, // 普通货物
  BATTERY = 2, // 带电货物
  POST_BOX = 3, // 投函货物
  SPECIAL = 6, // 特殊货物
}

// 运费模板接口
export interface ShippingFeeTemplate {
  id: number; // 模板ID
  name: string; // 模板名称
  type: ShippingTemplateType; // 模板类型
  typeName: string; // 模板类型名称
  firstWeightPrice: number; // 首重价格（元）
  firstWeightRange: number; // 首重范围（公斤）
  continuedWeightPrice: number; // 续重价格（元）
  continuedWeightInterval: number; // 续重区间大小（公斤）
  bulkCoefficient: number; // 轻抛系数
  threeSidesStart: number; // 三边和超过多少开始计算体积重量（厘米）
  createTime?: string; // 创建时间
  updateTime?: string; // 更新时间
}

// 用户运费模板响应数据
export interface UserShippingTemplatesData {
  userId: number;
  templates: ShippingFeeTemplate[];
}

// 用户运费模板响应接口
export interface UserShippingTemplatesResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: UserShippingTemplatesData;
}

// 可生成账单的用户信息接口
export interface BillingUserInfo {
  userId: number; // 用户ID
  nickname: string; // 用户昵称
  username: string; // 用户名
  manifestCount: number; // 可生成账单的运单数量
  adjustmentCount: number; // 财务调整记录数量
  totalCount: number; // 总数量
  hasManifests: boolean; // 是否有运单记录
  hasAdjustments: boolean; // 是否有调整记录
}

// 可生成账单用户列表响应数据
export interface BillingUsersData {
  total: number;
  list: BillingUserInfo[];
}

// 可生成账单用户列表响应接口
export interface BillingUsersResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: BillingUsersData;
}

// 生成账单请求参数接口
export interface GenerateBillingRequest {
  startTime: string; // 开始时间，格式：yyyy-MM-dd HH:mm:ss
  endTime: string; // 结束时间，格式：yyyy-MM-dd HH:mm:ss
  userId: number; // 用户ID
  generalTemplate?: ShippingFeeTemplate; // 普货模板信息
  batteryTemplate?: ShippingFeeTemplate; // 带电货物模板信息
  postBoxTemplate?: ShippingFeeTemplate; // 投函货物模板信息
  specialTemplate?: ShippingFeeTemplate; // 特殊货物模板信息
  dueDate?: string; // 付款截止日期，格式：yyyy-MM-dd
  notes?: string; // 账单备注
  currency: string; // 货币单位，默认CNY
}

// 生成账单响应数据
export interface GenerateBillingData {
  billingRecordId: number; // 生成的账单ID
  billNumber: string; // 账单编号
  totalAmount: number; // 账单总金额（元）
  itemsCount: number; // 明细项数量
  message: string; // 响应消息
}

// 生成账单响应接口
export interface GenerateBillingResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: GenerateBillingData;
}

/**
 * 查询可生成账单的用户列表
 * @param startTime 开始时间，格式：yyyy-MM-dd HH:mm:ss
 * @param endTime 结束时间，格式：yyyy-MM-dd HH:mm:ss
 * @returns 可生成账单的用户列表
 */
export const fetchBillingUsers = async (
  startTime: string,
  endTime: string
): Promise<BillingUsersData> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的try-catch
  const response = await apiClient.get<BillingUsersResponse>(
    getApiPath("/finance/billing/users"),
    {
      params: {
        startTime,
        endTime,
      },
    }
  );

  return response.data.data;
};

/**
 * 查询用户运费模板
 * @param userId 用户ID
 * @returns 用户运费模板列表
 */
export const fetchUserShippingTemplates = async (
  userId: number
): Promise<ShippingFeeTemplate[]> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的try-catch
  const response = await apiClient.get<UserShippingTemplatesResponse>(
    getApiPath(`/config/shipping-fee-templates/user/${userId}`)
  );

  return response.data.data.templates;
};

// 运费模板类型选项
export const SHIPPING_TEMPLATE_TYPE_OPTIONS = [
  { value: ShippingTemplateType.GENERAL, label: "普通货物" },
  { value: ShippingTemplateType.BATTERY, label: "带电货物" },
  { value: ShippingTemplateType.POST_BOX, label: "投函货物" },
  { value: ShippingTemplateType.SPECIAL, label: "特殊货物" },
];

/**
 * 格式化运费模板类型为中文显示
 * @param type 模板类型
 * @returns 中文类型描述
 */
export const formatShippingTemplateType = (
  type: ShippingTemplateType
): string => {
  const typeMap = {
    [ShippingTemplateType.GENERAL]: "普通货物",
    [ShippingTemplateType.BATTERY]: "带电货物",
    [ShippingTemplateType.POST_BOX]: "投函货物",
    [ShippingTemplateType.SPECIAL]: "特殊货物",
  };
  return typeMap[type] || "未知类型";
};

// 账单详情接口
export interface BillingRecordDetail {
  id: number; // 账单主键ID
  billNumber: string; // 账单编号
  customerAccountId: number; // 客户账户ID
  customerNickname: string; // 客户昵称
  customerUsername: string; // 客户用户名
  billDate: string; // 账单日期（yyyy-MM-dd 格式）
  dueDate?: string; // 付款截止日期（yyyy-MM-dd 格式）
  billingPeriodStart: string; // 账期开始日期（yyyy-MM-dd 格式）
  billingPeriodEnd: string; // 账期结束日期（yyyy-MM-dd 格式）
  appliedFreightTemplates: {
    generalTemplate?: ShippingFeeTemplate;
    batteryTemplate?: ShippingFeeTemplate;
    postBoxTemplate?: ShippingFeeTemplate;
    specialTemplate?: ShippingFeeTemplate;
  }; // 应用的运费模板信息
  totalAmount: number; // 账单总金额
  freightChargesTotal: number; // 运费明细总费用
  adjustmentChargesTotal: number; // 调整明细总费用
  amountPaid: number; // 已付金额
  balanceDue: number; // 应付余额
  currency: string; // 货币单位
  status: string; // 账单状态
  statusName: string; // 账单状态名称
  paymentMethod?: string; // 支付方式
  paymentTransactionId?: string; // 支付交易号
  paymentDate?: string; // 支付日期（yyyy-MM-dd HH:mm:ss 格式）
  notes?: string; // 账单备注
  generatedByUserId?: number; // 账单生成操作员ID
  generatedByNickname?: string; // 账单生成操作员昵称
  createTime: string; // 记录创建时间（yyyy-MM-dd HH:mm:ss 格式）
  updateTime: string; // 记录更新时间（yyyy-MM-dd HH:mm:ss 格式）
}

// 账单详情响应接口
export interface BillingRecordDetailResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: {
    billingRecord: BillingRecordDetail;
  };
}

// 账单明细项接口
export interface BillingRecordItem {
  id: number; // 账单明细主键ID
  billingRecordId: number; // 所属账单记录ID
  manifestId?: number; // 原始运单ID
  expressNumber?: string; // 快递单号
  orderNo?: string; // 系统订单号
  transferredTrackingNumber?: string; // 转单号
  orderNumber?: string; // 客户订单号
  manifestCreateTime?: string; // 运单创建时间（yyyy-MM-dd HH:mm:ss）
  shipmentTime?: string; // 发货时间（yyyy-MM-dd HH:mm:ss）
  receiverName?: string; // 收件人姓名
  itemDescription: string; // 物品描述
  cargoType: number; // 货物类型：1-普通、2-带电、3-投函
  cargoTypeName: string; // 货物类型名称
  weight?: number; // 实际重量(KG)
  length?: number; // 长(cm)
  width?: number; // 宽(cm)
  height?: number; // 高(cm)
  sumOfSides?: number; // 三边和(cm)
  dimensionalWeight?: number; // 体积重(KG)
  chargeableWeight?: number; // 计费重量(KG)
  baseFreightFee?: number; // 基础运费
  firstWeightFee?: number; // 首重费用
  continuedWeightFee?: number; // 续重费用
  overLengthSurcharge?: number; // 超长费
  overweightSurcharge?: number; // 超重费
  remoteAreaSurcharge?: number; // 偏远费
  itemTotalAmount: number; // 明细总费用
  createTime: string; // 创建时间（yyyy-MM-dd HH:mm:ss）
  updateTime: string; // 更新时间（yyyy-MM-dd HH:mm:ss）
}

// 账单明细查询参数接口
export interface BillingRecordItemsQueryParams {
  page?: number; // 页码，最小值为1
  pageSize?: number; // 每页数量，取值范围1-100
}

// 账单明细分页数据接口
export interface BillingRecordItemsPageData {
  total: number; // 明细总数
  list: BillingRecordItem[]; // 明细列表
}

// 账单明细响应接口
export interface BillingRecordItemsResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: BillingRecordItemsPageData;
}

/**
 * 根据账单记录ID获取账单详情
 * @param billingRecordId 账单记录ID
 * @returns 账单详情
 */
export const fetchBillingRecordDetail = async (
  billingRecordId: number
): Promise<BillingRecordDetail> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的try-catch
  const response = await apiClient.get<BillingRecordDetailResponse>(
    getApiPath(`/finance/billing/records/${billingRecordId}`)
  );

  return response.data.data.billingRecord;
};

/**
 * 根据账单记录ID分页查询账单明细
 * @param billingRecordId 账单记录ID
 * @param params 查询参数
 * @returns 账单明细分页数据
 */
export const fetchBillingRecordItems = async (
  billingRecordId: number,
  params: BillingRecordItemsQueryParams = {}
): Promise<BillingRecordItemsPageData> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的try-catch
  const response = await apiClient.get<BillingRecordItemsResponse>(
    getApiPath(`/finance/billing/records/${billingRecordId}/items`),
    { params }
  );

  return response.data.data;
};

// 货物类型选项，用于显示
export const CARGO_TYPE_OPTIONS = [
  { value: 1, label: "普通货物" },
  { value: 2, label: "带电货物" },
  { value: 3, label: "投函货物" },
  { value: 6, label: "特殊货物" },
];

/**
 * 格式化货物类型为中文显示
 * @param cargoType 货物类型
 * @returns 中文类型描述
 */
export const formatCargoType = (cargoType: number): string => {
  const typeMap = {
    1: "普通货物",
    2: "带电货物",
    3: "投函货物",
    6: "特殊货物",
  };
  return typeMap[cargoType as keyof typeof typeMap] || "未知类型";
};

// 财务调整快照接口
export interface BillingAdjustmentSnapshot {
  id: number; // 快照主键ID
  billingRecordId: number; // 账单记录ID
  originalAdjustmentId: number; // 原始调整记录ID
  adjustmentType: string; // 调整类型代码
  adjustmentTypeName: string; // 调整类型名称
  description: string; // 调整描述
  additionalDetails?: Record<string, unknown>; // 附加详情
  amount: number; // 调整金额
  currency: string; // 货币单位
  effectiveDate: string; // 生效日期
  manifestId?: number; // 运单ID
  manifestExpressNumber?: string; // 快递单号
  manifestOrderNo?: string; // 系统订单号
  manifestTransferredTrackingNumber?: string; // 转单号
  manifestCustomerOrderNumber?: string; // 客户订单号
  manifestCreateTime?: string; // 运单创建时间
  manifestShipmentTime?: string; // 发货时间
  manifestReceiverName?: string; // 收件人姓名
  manifestItemDescription?: string; // 物品描述
  manifestCargoType?: string; // 货物类型
  manifestWeight?: number; // 实际重量
  manifestLength?: number; // 长度
  manifestWidth?: number; // 宽度
  manifestHeight?: number; // 高度
  manifestSumOfSides?: number; // 三边和
  manifestDimensionalWeight?: number; // 体积重
  manifestChargeableWeight?: number; // 计费重量
  snapshotTime: string; // 快照时间
}

// 财务调整快照查询参数接口
export interface BillingAdjustmentSnapshotsQueryParams {
  page?: number; // 页码，最小值为1
  pageSize?: number; // 每页数量，取值范围1-100
}

// 财务调整快照分页数据接口
export interface BillingAdjustmentSnapshotsPageData {
  total: number; // 快照总数
  list: BillingAdjustmentSnapshot[]; // 快照列表
}

// 财务调整快照响应接口
export interface BillingAdjustmentSnapshotsResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: BillingAdjustmentSnapshotsPageData;
}

/**
 * 根据账单记录ID分页查询调整明细快照
 * @param billingRecordId 账单记录ID
 * @param params 查询参数
 * @returns 调整明细快照分页数据
 */
export const fetchBillingAdjustmentSnapshots = async (
  billingRecordId: number,
  params: BillingAdjustmentSnapshotsQueryParams = {}
): Promise<BillingAdjustmentSnapshotsPageData> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的try-catch
  const response = await apiClient.get<BillingAdjustmentSnapshotsResponse>(
    getApiPath(
      `/finance/billing/records/${billingRecordId}/adjustment-snapshots`
    ),
    { params }
  );

  return response.data.data;
};

// 调整类型枚举
export enum AdjustmentType {
  ADDITION = "ADDITION", // 增加
  REDUCTION = "REDUCTION", // 减少
  COMPENSATION = "COMPENSATION", // 赔偿
  REASSIGNMENT = "REASSIGNMENT", // 改派
  DESTRUCTION = "DESTRUCTION", // 销毁
  RETURN = "RETURN", // 退回
}

// 调整类型选项，用于筛选下拉框
export const ADJUSTMENT_TYPE_OPTIONS = [
  { value: AdjustmentType.ADDITION, label: "增加" },
  { value: AdjustmentType.REDUCTION, label: "减少" },
  { value: AdjustmentType.COMPENSATION, label: "赔偿" },
  { value: AdjustmentType.REASSIGNMENT, label: "改派" },
  { value: AdjustmentType.DESTRUCTION, label: "销毁" },
  { value: AdjustmentType.RETURN, label: "退回" },
];

/**
 * 格式化调整类型为中文显示
 * @param adjustmentType 调整类型代码
 * @returns 中文类型描述
 */
export const formatAdjustmentType = (adjustmentType: string): string => {
  const typeMap: Record<string, string> = {
    [AdjustmentType.ADDITION]: "增加",
    [AdjustmentType.REDUCTION]: "减少",
    [AdjustmentType.COMPENSATION]: "赔偿",
    [AdjustmentType.REASSIGNMENT]: "改派",
    [AdjustmentType.DESTRUCTION]: "销毁",
    [AdjustmentType.RETURN]: "退回",
  };
  return typeMap[adjustmentType] || adjustmentType;
};

// 导出账单请求参数接口
export interface ExportBillingRecordRequest {
  billingRecordId: number; // 账单记录ID，必须大于0
}

/**
 * 导出账单Excel文件
 * @param billingRecordId 账单记录ID
 * @returns Promise<Blob> Excel文件的二进制数据
 */
export const exportBillingRecord = async (
  billingRecordId: number
): Promise<Blob> => {
  try {
    const response = await apiClient.post(
      getApiPath("/finance/billing-record/export"),
      { billingRecordId },
      {
        responseType: "blob", // 指定响应类型为blob
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    // 检查响应是否为Excel文件
    const contentType = response.headers["content-type"];
    if (
      !contentType ||
      !contentType.includes(
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      )
    ) {
      // 如果不是Excel文件，可能是错误响应，尝试解析JSON错误信息
      const text = await response.data.text();
      try {
        const errorData = JSON.parse(text);
        throw new Error(errorData.errorMessage || "导出账单失败");
      } catch {
        throw new Error("导出账单失败，响应格式错误");
      }
    }

    return response.data;
  } catch (error) {
    console.error("Failed to export billing record:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error("导出账单失败，请重试");
  }
};

/**
 * 下载账单Excel文件
 * @param billingRecordId 账单记录ID
 * @param filename 文件名（可选，如果不提供会从响应头获取）
 */
export const downloadBillingRecord = async (
  billingRecordId: number,
  filename?: string
): Promise<void> => {
  try {
    const response = await apiClient.post(
      getApiPath("/finance/billing-record/export"),
      { billingRecordId },
      {
        responseType: "blob",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    // 创建下载链接
    const blob = response.data;
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;

    // 从响应头获取文件名或使用默认文件名
    let downloadFilename = filename;
    if (!downloadFilename) {
      const contentDisposition = response.headers["content-disposition"];
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/);
        if (filenameMatch) {
          downloadFilename = decodeURIComponent(filenameMatch[1]);
        }
      }
    }
    link.download = downloadFilename || `账单明细_${billingRecordId}.xlsx`;

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error("Failed to download billing record:", error);
    throw error;
  }
};

/**
 * 生成账单
 * @param params 生成账单参数
 * @returns 生成账单结果
 */
export const generateBilling = async (
  params: GenerateBillingRequest
): Promise<GenerateBillingData> => {
  // apiClient的拦截器已经处理了错误情况，这里不需要额外的try-catch
  const response = await apiClient.post<GenerateBillingResponse>(
    getApiPath("/finance/billing/generate"),
    params
  );

  return response.data.data;
};

// 账单生成任务状态枚举
export enum BillingTaskStatus {
  PENDING = "PENDING", // 待处理
  PROCESSING = "PROCESSING", // 处理中
  COMPLETED = "COMPLETED", // 已完成
  FAILED = "FAILED", // 失败
}

// 用户模板配置接口
export interface UserTemplateConfig {
  userId: number;
  generalTemplate?: ShippingFeeTemplate;
  batteryTemplate?: ShippingFeeTemplate;
  postBoxTemplate?: ShippingFeeTemplate;
  specialTemplate?: ShippingFeeTemplate;
}

// 异步生成账单请求参数接口
export interface AsyncGenerateBillingRequest {
  startTime: string; // 开始时间，格式：yyyy-MM-dd HH:mm:ss
  endTime: string; // 结束时间，格式：yyyy-MM-dd HH:mm:ss
  customerIds: number[]; // 客户ID数组
  billingCycleId?: number; // 账期批次ID（可选）
  dueDate?: string; // 付款截止日期，格式：yyyy-MM-dd
  currency?: string; // 货币单位，默认CNY
  notes?: string; // 账单备注
  userTemplates?: UserTemplateConfig[]; // 用户自定义模板配置（可选）
}

// 账单生成任务数据接口
export interface BillingTaskData {
  taskId: string; // 任务UUID
  billingCycleId?: number; // 关联的账期批次ID
  customerCount: number; // 客户数量
  status: BillingTaskStatus; // 任务状态
  statusName: string; // 任务状态中文名称
  submitTime: string; // 提交时间
  message: string; // 响应消息
}

// 异步生成账单响应接口
export interface AsyncGenerateBillingResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: BillingTaskData;
}

// 账单生成任务详情接口
export interface BillingTaskDetail {
  taskId: string; // 任务UUID
  billingCycleId?: number; // 关联的账期批次ID
  targetCustomerIds: string; // 目标客户ID列表（逗号分隔字符串）
  targetCustomerList: number[]; // 解析后的目标客户ID列表
  status: BillingTaskStatus; // 任务状态
  statusName: string; // 任务状态中文名称
  progressPercentage: number; // 进度百分比（0-100）
  totalItemsToProcess: number; // 预计总处理项数
  itemsProcessedCount: number; // 已处理项数
  errorMessage?: string; // 错误信息（失败时）
  submittedByUserId?: number; // 任务提交者ID
  submittedByNickname?: string; // 任务提交者昵称
  submittedByUsername?: string; // 任务提交者用户名
  submitTime: string; // 提交时间
  startTime?: string; // 处理开始时间
  endTime?: string; // 处理结束时间
  duration?: number; // 执行耗时（秒）
  isCompleted: boolean; // 是否已完成
  isFailed: boolean; // 是否失败
  isProcessing: boolean; // 是否正在处理
  isPending: boolean; // 是否待处理
  customerCount: number; // 客户数量
  progressDescription: string; // 进度描述
}

// 账单生成任务详情响应接口
export interface BillingTaskDetailResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: {
    task: BillingTaskDetail;
  };
}

/**
 * 异步生成账单
 * @param params 生成参数
 * @returns 任务信息
 */
export const asyncGenerateBilling = async (
  params: AsyncGenerateBillingRequest
): Promise<BillingTaskData> => {
  const response = await apiClient.post<AsyncGenerateBillingResponse>(
    getApiPath("/finance/billing/async-generate"),
    params
  );

  return response.data.data;
};

/**
 * 根据任务ID获取任务详情
 * @param taskId 任务ID
 * @returns 任务详情
 */
export const fetchBillingTaskDetail = async (
  taskId: string
): Promise<BillingTaskDetail> => {
  const response = await apiClient.get<BillingTaskDetailResponse>(
    getApiPath(`/finance/billing/generation-tasks/${taskId}`)
  );

  return response.data.data.task;
};

/**
 * 格式化任务状态为中文显示
 * @param status 任务状态
 * @returns 中文状态描述
 */
export const formatBillingTaskStatus = (status: BillingTaskStatus): string => {
  const statusMap = {
    [BillingTaskStatus.PENDING]: "待处理",
    [BillingTaskStatus.PROCESSING]: "处理中",
    [BillingTaskStatus.COMPLETED]: "已完成",
    [BillingTaskStatus.FAILED]: "失败",
  };
  return statusMap[status] || "未知状态";
};

// 账单生成任务列表项接口
export interface BillingTaskItem {
  taskId: string; // 任务UUID
  billingCycleId: number; // 关联的账期批次ID
  targetCustomerIds: string; // 目标客户ID列表（逗号分隔字符串）
  status: BillingTaskStatus; // 任务状态
  statusName: string; // 任务状态中文名称
  progressPercentage: number; // 进度百分比（0-100）
  totalItemsToProcess: number; // 预计总处理项数
  itemsProcessedCount: number; // 已处理项数
  errorMessage?: string; // 错误信息（失败时）
  submittedByUserId?: number; // 任务提交者ID
  submittedByNickname?: string; // 任务提交者昵称
  submitTime: string; // 提交时间
  startTime?: string; // 处理开始时间
  endTime?: string; // 处理结束时间
  duration?: number; // 执行耗时（秒）
}

// 账单生成任务列表查询参数接口
export interface BillingTaskListQueryParams {
  billingCycleId: number; // 账期批次ID，必填
  page?: number; // 页码，默认1
  pageSize?: number; // 每页数量，默认20，最大100
}

// 账单生成任务列表分页数据接口
export interface BillingTaskListPageData {
  total: number; // 任务总数
  list: BillingTaskItem[]; // 任务列表
}

// 账单生成任务列表响应接口
export interface BillingTaskListResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: BillingTaskListPageData;
}

/**
 * 查询账单生成任务列表
 * @param params 查询参数
 * @returns 任务列表分页数据
 */
export const fetchBillingTaskList = async (
  params: BillingTaskListQueryParams
): Promise<BillingTaskListPageData> => {
  const response = await apiClient.get<BillingTaskListResponse>(
    getApiPath("/finance/billing/generation-tasks"),
    { params }
  );

  return response.data.data;
};
