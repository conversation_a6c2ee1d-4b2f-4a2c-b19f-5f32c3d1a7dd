/* ManifestStyles.css - 运单管理系统样式 */

/* 表格斑马纹样式 */
.zebra-stripe {
  background-color: #f9fafb !important; /* 浅灰色背景 */
}

/* 鼠标悬停行效果 */
.ant-table-tbody > tr:hover > td {
  background-color: #edf2f7 !important; /* 保持一致的悬停效果 */
}

/* 收件人信息样式 */
.receiver-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
  min-width: 0; /* 确保Flex子元素可以收缩 */
}

.receiver-info .ant-flex {
  align-items: center;
  gap: 4px;
  width: 100%;
  min-width: 0; /* 确保Flex子元素可以收缩 */
}

/* 地址容器样式 */
.address-container {
  width: 100%;
  overflow: hidden;
}

.receiver-info .address-info {
  flex: 1;
  overflow: hidden;
  width: 100%;
  min-width: 0; /* 确保元素可以收缩 */
}

/* 地址文本显示方式 - 两种选择 */
/* 选项1: 使用省略号隐藏超长部分（默认） */
.receiver-info .address-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 12px;
  color: #666;
  width: 100%;
}

/* 选项2: 自动换行显示全部地址 */
.receiver-info .address-text-wrap {
  word-break: break-all; /* 允许在单词内换行，确保长单词不会溢出 */
  white-space: normal; /* 允许正常换行 */
  font-size: 12px;
  color: #666;
  width: 100%;
  line-height: 1.3; /* 调整行高以便阅读 */
  /* 移除 max-height 和 overflow: hidden 以显示完整地址 */
}

/* 时间线样式 */
.compact-timeline {
  margin: 0 !important;
  padding: 0 !important;
  font-size: 12px;
}

.compact-timeline .ant-timeline-item {
  padding-bottom: 6px;
  min-height: auto;
}

.compact-timeline .ant-timeline-item-content {
  margin-inline-start: 10px !important;
}

.timeline-item {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.timeline-label {
  font-weight: 500;
  white-space: nowrap;
  color: #333;
}

.timeline-time {
  color: #666;
  font-size: 11px;
}

.text-muted {
  color: #999;
  font-size: 12px;
}

/* 可复制文本样式 */
.copyable-text {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s;
}

.copyable-text:hover {
  color: #1890ff;
}

.copyable-text .text {
  margin-right: 4px;
  overflow: hidden; /* 默认保持单行溢出隐藏 */
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 商家订单号的特定换行样式 */
.copyable-text.merchant-order-text .text {
  white-space: normal; /* 允许正常换行 */
  word-break: break-all; /* 允许在单词内换行 */
  overflow: visible; /* 确保内容可见 */
  text-overflow: clip; /* 移除省略号 */
}

/* 通用可换行CopyableText样式 */
.copyable-text.wrappable-copyable-text .text {
  white-space: normal;
  word-break: break-all;
  overflow: visible;
  text-overflow: clip;
  font-size: 14px; /* 将字体大小从 12px 调整为 14px */
  line-height: 1.4; /* 相应调整行高 */
}

/* 单号标签样式 */
.order-label {
  font-weight: 500;
  font-size: 11px;
  color: #555;
  margin-right: 4px;
  white-space: nowrap; /* 确保标签本身不换行 */
}

.copyable-text .copy-icon {
  color: #1890ff;
}

.copyable-text .copied-icon {
  color: #52c41a;
}

/* 操作按钮 */
.action-buttons .ant-btn {
  margin-right: 8px;
}

/* 搜索表单样式 */
.search-form .ant-form-item {
  margin-bottom: 0;
}

.search-form .search-buttons {
  display: flex;
  justify-content: flex-end;
}

.search-form .ant-input:focus,
.search-form .ant-select-focused .ant-select-selector {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 模态框和抽屉样式 */
.manifest-detail-drawer .ant-descriptions {
  margin-bottom: 20px;
}

.manifest-detail-drawer .ant-descriptions-title {
  font-size: 16px;
  margin-bottom: 12px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .search-form .search-buttons {
    margin-top: 16px;
    /* 在移动视图下仍然保持靠右对齐 */
    display: flex;
    justify-content: flex-end;
  }
}

/* 标记有转单号的行 */
.has-transferred-tracking-number td {
  background-color: #fffbe6 !important; /* 浅黄色背景，确保覆盖其他背景样式 */
}

/* 如果希望斑马纹与转单号标记共存，且转单号标记优先，可以这样做： */
/* 
.zebra-stripe.has-transferred-tracking-number td {
  background-color: #fffbe6 !important; 
}
.has-transferred-tracking-number td {
  background-color: #fffbe6 !important; 
}
*/

/* 或者，如果希望斑马纹在有转单号时不显示，则上面的规则已足够 */
