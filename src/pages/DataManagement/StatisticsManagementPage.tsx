import React, { useState } from 'react';
import {
  Card,
  Form,
  Row,
  Col,
  Input,
  Button,
  DatePicker,
  Select,
  Typography,
  Divider,
  Alert,
  Space,
  Spin,
  Result,
  Statistic,
  Table,
  Tag,
  App,
} from 'antd';
import {
  ReloadOutlined,
  BarChartOutlined,
  CalendarOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import { 
  regenerateStatistics, 
  RegenerateStatisticsParams, 
  RegenerateStatisticsData 
} from '../../services/statisticsService';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 统计管理页面组件
 * 用于重新统计发货量数据
 */
const StatisticsManagementPage: React.FC = () => {
  const [form] = Form.useForm();
  const { message } = App.useApp();
  
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [resultData, setResultData] = useState<RegenerateStatisticsData | null>(null);
  const [showResult, setShowResult] = useState(false);

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setResultData(null);
    setShowResult(false);
  };

  // 处理重新统计
  const handleRegenerate = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      setShowResult(false);
      
      const params: RegenerateStatisticsParams = {};
      
      // 根据统计范围类型设置参数
      if (values.rangeType === 'year' && values.year) {
        params.year = dayjs(values.year).year();
      } else if (values.rangeType === 'month' && values.month) {
        const monthDate = dayjs(values.month);
        params.year = monthDate.year();
        params.month = monthDate.month() + 1; // dayjs的月份从0开始
      } else if (values.rangeType === 'day' && values.day) {
        const dayDate = dayjs(values.day);
        params.year = dayDate.year();
        params.month = dayDate.month() + 1;
        params.day = dayDate.date();
      }
      // 如果是all类型，不设置任何参数
      
      console.log('重新统计参数:', params);
      
      const result = await regenerateStatistics(params);
      setResultData(result);
      setShowResult(true);
      
      message.success('重新统计完成！');
    } catch (error: any) {
      console.error('重新统计失败:', error);
      message.error(`重新统计失败: ${error.message || '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 统计范围类型变化处理
  const handleRangeTypeChange = (value: string) => {
    // 清除相关字段
    form.setFieldsValue({
      year: undefined,
      month: undefined,
      day: undefined,
    });
  };

  // 渲染统计结果
  const renderResult = () => {
    if (!showResult || !resultData) return null;
    
    return (
      <Card title="统计结果" style={{ marginTop: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Statistic 
              title="处理天数" 
              value={resultData.processedDays} 
              suffix="天"
              prefix={<CalendarOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Statistic 
              title="涉及用户数" 
              value={resultData.totalUsers} 
              suffix="个"
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Statistic 
              title="总发货量" 
              value={resultData.totalShipments} 
              suffix="单"
              prefix={<BarChartOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Statistic 
              title="总运费金额" 
              value={resultData.totalFee} 
              precision={2}
              suffix="元"
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Statistic 
              title="处理耗时" 
              value={resultData.duration}
            />
          </Col>
          <Col xs={24} sm={12} md={8}>
            <div>
              <Text strong>统计范围</Text>
              <br />
              <Text type="secondary">
                {resultData.startDate} 至 {resultData.endDate}
              </Text>
            </div>
          </Col>
        </Row>
        
        <Divider />
        
        <Alert
          message={resultData.message}
          type="success"
          showIcon
        />
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <BarChartOutlined /> 统计管理
      </Title>
      <Text type="secondary" style={{ fontSize: '16px', display: 'block', marginBottom: '24px' }}>
        重新统计指定时间范围的发货数据，支持初始化历史数据、修正错误统计数据或手动触发特定日期的统计。
      </Text>

      <Card title="重新统计发货量数据" bordered={false}>
        <Alert
          message="注意事项"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>重新统计会覆盖已有的统计数据</li>
              <li>只能选择昨天及之前的日期，不能选择今天或未来日期</li>
              <li>大范围统计可能需要较长时间，请耐心等待</li>
              <li>建议在业务低峰期进行大范围统计操作</li>
              <li>如果不选择任何范围，将统计从2020年1月1日到昨天的所有数据</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleRegenerate}
          initialValues={{
            rangeType: 'all'
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} md={8}>
              <Form.Item
                label="统计范围类型"
                name="rangeType"
                rules={[{ required: true, message: '请选择统计范围类型' }]}
              >
                <Select 
                  placeholder="请选择统计范围类型"
                  onChange={handleRangeTypeChange}
                >
                  <Option value="all">全部历史数据</Option>
                  <Option value="year">指定年份</Option>
                  <Option value="month">指定月份</Option>
                  <Option value="day">指定日期</Option>
                </Select>
              </Form.Item>
            </Col>

            <Form.Item shouldUpdate noStyle>
              {({ getFieldValue }) => {
                const rangeType = getFieldValue('rangeType');
                
                if (rangeType === 'year') {
                  return (
                    <Col xs={24} md={8}>
                      <Form.Item
                        label="年份"
                        name="year"
                        rules={[{ required: true, message: '请选择年份' }]}
                      >
                        <DatePicker 
                          picker="year" 
                          placeholder="选择年份"
                          style={{ width: '100%' }}
                          disabledDate={(current) => current && current >= dayjs().startOf('day')}
                        />
                      </Form.Item>
                    </Col>
                  );
                }
                
                if (rangeType === 'month') {
                  return (
                    <Col xs={24} md={8}>
                      <Form.Item
                        label="月份"
                        name="month"
                        rules={[{ required: true, message: '请选择月份' }]}
                      >
                        <DatePicker 
                          picker="month" 
                          placeholder="选择月份"
                          style={{ width: '100%' }}
                          disabledDate={(current) => current && current >= dayjs().startOf('day')}
                        />
                      </Form.Item>
                    </Col>
                  );
                }
                
                if (rangeType === 'day') {
                  return (
                    <Col xs={24} md={8}>
                      <Form.Item
                        label="日期"
                        name="day"
                        rules={[{ required: true, message: '请选择日期' }]}
                      >
                        <DatePicker 
                          placeholder="选择日期"
                          style={{ width: '100%' }}
                          disabledDate={(current) => current && current >= dayjs().startOf('day')}
                        />
                      </Form.Item>
                    </Col>
                  );
                }
                
                return null;
              }}
            </Form.Item>

            <Col xs={24}>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<ReloadOutlined />}
                    size="large"
                  >
                    开始重新统计
                  </Button>
                  <Button 
                    onClick={handleReset}
                    disabled={loading}
                    size="large"
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>

        {loading && (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>
              <Text>正在重新统计数据，请稍候...</Text>
            </div>
          </div>
        )}

        {renderResult()}
      </Card>
    </div>
  );
};

export default StatisticsManagementPage; 