import React from "react";
import { Badge, Space } from "antd";
import { useSharedPendingCount } from "../contexts/PendingCountContext";

interface MenuBadgeProps {
  title: string;
}

/**
 * 带徽标的菜单项组件，显示待审核数量
 */
const MenuBadge: React.FC<MenuBadgeProps> = ({ title }) => {
  const { pendingCount, loading } = useSharedPendingCount();

  return (
    <Space>
      {title}
      {pendingCount > 0 && !loading && (
        <Badge count={pendingCount} size="small" style={{ marginBottom: 2 }} />
      )}
    </Space>
  );
};

export default MenuBadge;
