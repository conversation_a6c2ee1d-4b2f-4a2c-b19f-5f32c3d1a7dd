import axios, { AxiosError, AxiosResponse } from "axios";
import eventBus from "../utils/eventBus"; // 导入事件总线

// Centralized ApiResponse interface
export interface ApiResponse<T> {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: T | null;
}

// 定义API错误类型，用于携带错误码信息
export class ApiError extends Error {
  errorCode: number;

  constructor(message: string, errorCode: number) {
    super(message);
    this.name = "ApiError";
    this.errorCode = errorCode;
  }
}

// 判断是否为开发环境
const isDevelopment = import.meta.env.DEV;

console.log("当前环境:", isDevelopment ? "开发环境" : "生产环境");
console.log("API前缀:", isDevelopment ? "/api" : "/admin-api");

// 全局message实例，将在App组件中设置
let globalMessageInstance: { error: (message: string) => void } | null = null;

// 设置全局message实例
export const setGlobalMessageInstance = (
  messageInstance: { error: (message: string) => void } | null
) => {
  globalMessageInstance = messageInstance;
};

// 显示错误消息的辅助函数
const showErrorMessage = (message: string) => {
  console.log("🔴 尝试显示错误消息:", message);
  if (globalMessageInstance) {
    globalMessageInstance.error(message);
  } else {
    console.warn("⚠️ 全局message实例未设置，无法显示错误提示:", message);
    // 降级处理：使用alert作为备选方案
    alert(`错误: ${message}`);
  }
};

// --- apiClient (with global error messaging) ---
// This client returns the full AxiosResponse. Callers should access .data
// The interceptor throws an error on business failure (checked via response.data)
export const apiClient = axios.create({
  baseURL: "", // 置空baseURL，由getApiPath函数处理路径前缀
  timeout: 30000,
  headers: { "Content-Type": "application/json" },
});

apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("authToken");
    if (token) config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);

apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse<unknown>>) => {
    const apiResponse = response.data; // apiResponse is of type ApiResponse<unknown>
    if (
      apiResponse &&
      typeof apiResponse.success === "boolean" &&
      !apiResponse.success
    ) {
      // 检查是否是特定的未授权错误码，例如 100004 (未授权/未登录)
      // 或者直接检查 HTTP 状态码，如下面的 error.response.status === 401
      if (apiResponse.errorCode === 100004) {
        eventBus.emit("sessionExpired");
        // 对于业务上明确的未登录/token失效导致的失败，直接返回一个reject，避免后续的全局错误提示
        // 同时阻止将此作为普通ApiError抛出，以免触发其他通用错误处理
        return Promise.reject(
          new ApiError(
            apiResponse.errorMessage || "会话已过期",
            apiResponse.errorCode
          )
        );
      }

      // 对于其他业务错误，显示错误提示给用户
      const errorMessage = apiResponse.errorMessage || "操作失败";
      console.log("🔴 apiClient拦截器: 显示业务错误提示", errorMessage);
      showErrorMessage(errorMessage);
      throw new ApiError(errorMessage, apiResponse.errorCode);
    }
    if (
      apiResponse &&
      apiResponse.errorCode &&
      apiResponse.errorCode !== 100000
    ) {
      if (apiResponse.errorCode === 100004) {
        // 再次检查，以防 success 为 true 但 errorCode 指示未授权
        eventBus.emit("sessionExpired");
        return Promise.reject(
          new ApiError(
            apiResponse.errorMessage || "会话已过期",
            apiResponse.errorCode
          )
        );
      }

      // 对于其他错误码，显示错误提示给用户
      const errorMessage = apiResponse.errorMessage || "发生错误";
      console.log("🔴 apiClient拦截器: 显示错误码提示", errorMessage);
      showErrorMessage(errorMessage);
      throw new ApiError(errorMessage, apiResponse.errorCode);
    }
    return response; // Return full AxiosResponse
  },
  (error: AxiosError<ApiResponse<unknown>>) => {
    if (error.response) {
      // 检查 HTTP 状态码是否为 401
      if (error.response.status === 401) {
        // 检查是否已经因为 eventBus.emit("sessionExpired") 从 response 拦截器中 reject
        // 如果 error.message 已经是 "会话已过期" 或特定标识，则不再 emit
        // 但通常情况下，HTTP 401 错误会直接进入此错误拦截器
        const errorData = error.response.data;
        let errorMessage = "会话已过期或无权限访问";
        let errorCode = 401;

        if (
          errorData &&
          typeof errorData.success === "boolean" &&
          errorData.errorMessage
        ) {
          errorMessage = errorData.errorMessage;
          errorCode = errorData.errorCode || 401;
        } else if (
          errorData &&
          typeof errorData === "object" &&
          errorData !== null &&
          "message" in errorData &&
          typeof (errorData as { message?: unknown }).message === "string"
        ) {
          errorMessage = (errorData as { message: string }).message;
        }

        // 只有在不是由业务逻辑（success: false, errorCode: 100004）触发的会话过期时才再次 emit
        // 避免重复触发。理想情况下，后端对401的响应体也应遵循ApiResponse结构
        if (errorCode !== 100004) {
          // 如果errorCode不是特定的100004，但状态是401，也触发
          eventBus.emit("sessionExpired");
        }
        // 对于401错误，我们不使用 antd 的 message.error，因为会有专门的弹窗
        // 直接抛出错误，让 App.tsx 中的弹窗处理
        throw new ApiError(errorMessage, errorCode);
      }

      const errorData = error.response.data;

      // 优先处理标准ApiResponse结构的错误
      if (errorData && typeof errorData.success === "boolean") {
        // 对于其他错误，如果 errorCode 是 100004，也触发会话过期
        if (errorData.errorCode === 100004) {
          eventBus.emit("sessionExpired");
          // 抛出错误以中断当前操作流程
          throw new ApiError(
            errorData.errorMessage || "会话已过期",
            errorData.errorCode
          );
        }
        // 其他业务错误，使用 message.error 提示
        console.log(
          "🔴 apiClient拦截器: 显示HTTP业务错误提示",
          errorData.errorMessage || `请求错误 (${error.response.status})`
        );
        showErrorMessage(
          errorData.errorMessage || `请求错误 (${error.response.status})`
        );
        throw new ApiError(
          errorData.errorMessage || `请求错误 (${error.response.status})`,
          errorData.errorCode || error.response.status
        );
      }

      // 处理非标准ApiResponse结构但包含errorMessage的情况
      if (
        errorData &&
        typeof errorData === "object" &&
        errorData !== null &&
        "errorMessage" in errorData &&
        typeof (errorData as { errorMessage?: unknown }).errorMessage ===
          "string"
      ) {
        const errorMessage = (errorData as { errorMessage: string })
          .errorMessage;
        const errorCode =
          (errorData as { errorCode?: number }).errorCode ||
          error.response.status;
        showErrorMessage(errorMessage);
        throw new ApiError(errorMessage, errorCode);
      }

      // 处理包含message字段的错误响应
      if (
        errorData &&
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof (errorData as { message?: unknown }).message === "string"
      ) {
        const errorMessage = (errorData as { message: string }).message;
        showErrorMessage(errorMessage);
        throw new ApiError(errorMessage, error.response.status);
      }

      // 对于HTTP 500状态码，特别处理以确保显示有意义的错误信息
      if (error.response.status === 500) {
        let errorMessage = "服务器内部错误";

        // 尝试从响应体中提取错误信息
        if (errorData) {
          if (typeof errorData === "string") {
            errorMessage = errorData;
          } else if (typeof errorData === "object" && errorData !== null) {
            // 尝试多种可能的错误信息字段
            const possibleErrorFields = [
              "errorMessage",
              "message",
              "error",
              "detail",
            ];
            for (const field of possibleErrorFields) {
              if (
                field in errorData &&
                typeof (errorData as unknown as Record<string, unknown>)[
                  field
                ] === "string"
              ) {
                errorMessage = (errorData as unknown as Record<string, string>)[
                  field
                ];
                break;
              }
            }
          }
        }

        console.log("🔴 apiClient拦截器: 显示HTTP 500错误提示", errorMessage);
        showErrorMessage(errorMessage);
        throw new ApiError(errorMessage, 500);
      }

      // 对于其他HTTP状态码的非业务错误（例如网络层错误但有 response）
      showErrorMessage(
        `服务请求错误 ${error.response.status}: ${error.message}`
      );
      throw new ApiError(
        `服务请求错误 ${error.response.status}: ${error.message}`,
        error.response.status
      );
    } else if (error.request) {
      showErrorMessage("网络连接失败，请稍后重试");
      throw new ApiError("网络连接失败，请稍后重试", 0);
    } else {
      showErrorMessage(`请求发送失败: ${error.message}`);
      throw new ApiError(`请求发送失败: ${error.message}`, 0);
    }
  }
);

// --- rawApiClient (no global data unwrapping or business error throwing) ---
export const rawApiClient = axios.create({
  baseURL: "", // 置空baseURL，由getApiPath函数处理路径前缀
  timeout: 10000,
  headers: { "Content-Type": "application/json" },
});

rawApiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("authToken");
    if (token) config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  (error: AxiosError) => Promise.reject(error)
);
