import React, { useState, useEffect, useCallback } from "react";
import {
  Modal,
  Table,
  Typography,
  Tag,
  Tooltip,
  Space,
  Button,
  Empty,
  message,
  Image,
} from "antd";
import {
  ReloadOutlined,
  DollarOutlined,
  FileTextOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
  fetchBillingAdjustmentSnapshots,
  BillingAdjustmentSnapshot,
  BillingAdjustmentSnapshotsPageData,
  AdjustmentType,
  formatAdjustmentType,
} from "../../services/billingService";

const { Text } = Typography;

// 组件属性接口
interface BillingAdjustmentModalProps {
  visible: boolean;
  billingRecordId: number;
  billNumber: string;
  onClose: () => void;
}

const BillingAdjustmentModal: React.FC<BillingAdjustmentModalProps> = ({
  visible,
  billingRecordId,
  billNumber,
  onClose,
}) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [adjustmentData, setAdjustmentData] =
    useState<BillingAdjustmentSnapshotsPageData>({
      total: 0,
      list: [],
    });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  /**
   * 检查数值是否有效且大于0
   */
  const isValidPositiveNumber = (
    value: number | string | null | undefined
  ): boolean => {
    if (value == null || value === "" || value === "0" || value === 0) {
      return false;
    }
    const num = Number(value);
    return !isNaN(num) && num > 0;
  };

  /**
   * 加载调整记录数据
   */
  const loadAdjustmentData = useCallback(
    async (page: number = currentPage, size: number = pageSize) => {
      if (!billingRecordId || isNaN(billingRecordId)) {
        return;
      }

      try {
        setLoading(true);
        const data = await fetchBillingAdjustmentSnapshots(billingRecordId, {
          page,
          pageSize: size,
        });
        setAdjustmentData(data);
      } catch (error) {
        console.error("获取调整记录失败:", error);
        message.error("获取调整记录失败，请重试");
      } finally {
        setLoading(false);
      }
    },
    [billingRecordId, currentPage, pageSize]
  );

  /**
   * 处理分页变化
   */
  const handleTableChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    loadAdjustmentData(page, size);
  };

  /**
   * 刷新数据
   */
  const handleRefresh = () => {
    loadAdjustmentData();
  };

  /**
   * 渲染调整类型标签
   */
  const renderAdjustmentTypeTag = (type: string) => {
    const colorMap: Record<string, string> = {
      [AdjustmentType.ADDITION]: "green",
      [AdjustmentType.REDUCTION]: "red",
      [AdjustmentType.COMPENSATION]: "orange",
      [AdjustmentType.REASSIGNMENT]: "blue",
      [AdjustmentType.DESTRUCTION]: "purple",
      [AdjustmentType.RETURN]: "cyan",
    };

    return (
      <Tag color={colorMap[type] || "default"} icon={<DollarOutlined />}>
        {formatAdjustmentType(type)}
      </Tag>
    );
  };

  /**
   * 渲染运单信息
   */
  const renderManifestInfo = (record: BillingAdjustmentSnapshot) => {
    const hasExpressNumber =
      record.manifestExpressNumber &&
      record.manifestExpressNumber.trim() !== "";
    const hasOrderNo =
      record.manifestOrderNo && record.manifestOrderNo.trim() !== "";
    const hasTransferredTrackingNumber =
      record.manifestTransferredTrackingNumber &&
      record.manifestTransferredTrackingNumber.trim() !== "";
    const hasCustomerOrderNumber =
      record.manifestCustomerOrderNumber &&
      record.manifestCustomerOrderNumber.trim() !== "";

    return (
      <div>
        {hasExpressNumber && (
          <div style={{ fontWeight: "bold", marginBottom: "2px" }}>
            <Tooltip title="快递单号">
              <span style={{ fontFamily: "monospace" }}>
                {record.manifestExpressNumber}
              </span>
            </Tooltip>
          </div>
        )}
        {hasOrderNo && (
          <div style={{ fontSize: "12px", color: "#666", marginBottom: "1px" }}>
            系统订单: {record.manifestOrderNo}
          </div>
        )}
        {hasTransferredTrackingNumber && (
          <div style={{ fontSize: "12px", color: "#666", marginBottom: "1px" }}>
            转单号: {record.manifestTransferredTrackingNumber}
          </div>
        )}
        {hasCustomerOrderNumber && (
          <div style={{ fontSize: "12px", color: "#666" }}>
            客户订单: {record.manifestCustomerOrderNumber}
          </div>
        )}
        {!hasExpressNumber &&
          !hasOrderNo &&
          !hasTransferredTrackingNumber &&
          !hasCustomerOrderNumber && <Text type="secondary">暂无运单信息</Text>}
      </div>
    );
  };

  /**
   * 表格列定义
   */
  const columns: ColumnsType<BillingAdjustmentSnapshot> = [
    {
      title: "调整信息",
      key: "adjustment",
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: "4px" }}>
            {renderAdjustmentTypeTag(record.adjustmentType)}
          </div>
          <div
            style={{
              fontWeight: "bold",
              color: (() => {
                switch (record.adjustmentType) {
                  case AdjustmentType.ADDITION:
                    return "#52c41a";
                  case AdjustmentType.REDUCTION:
                    return "#ff4d4f";
                  case AdjustmentType.COMPENSATION:
                    return "#fa8c16";
                  case AdjustmentType.REASSIGNMENT:
                    return "#1890ff";
                  case AdjustmentType.DESTRUCTION:
                    return "#722ed1";
                  case AdjustmentType.RETURN:
                    return "#13c2c2";
                  default:
                    return "#666";
                }
              })(),
            }}
          >
            {record.adjustmentType === AdjustmentType.ADDITION
              ? "+"
              : record.adjustmentType === AdjustmentType.REDUCTION
              ? "-"
              : ""}
            {Number(record.amount).toFixed(2)} {record.currency}
          </div>
          <div style={{ fontSize: "12px", color: "#666", marginTop: "2px" }}>
            生效日期: {record.effectiveDate}
          </div>
        </div>
      ),
    },
    {
      title: "调整描述",
      dataIndex: "description",
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (description: string) => {
        if (!description || description.trim() === "") {
          return <Text type="secondary">无描述</Text>;
        }
        return (
          <Tooltip title={description} placement="topLeft">
            <span>{description}</span>
          </Tooltip>
        );
      },
    },
    {
      title: "赔偿详情",
      key: "compensationDetails",
      width: 220,
      render: (_, record) => {
        // 只有赔偿类型才显示详情
        if (record.adjustmentType !== AdjustmentType.COMPENSATION) {
          return <Text type="secondary">-</Text>;
        }

        // 解析附加详情
        let details: Record<string, unknown> = {};
        try {
          if (record.additionalDetails) {
            details = record.additionalDetails;
            if (typeof details === "string") {
              details = JSON.parse(details);
            }
          }
        } catch (error) {
          console.error("解析赔偿详情失败:", error);
          return <Text type="secondary">详情解析失败</Text>;
        }

        const cargoValue =
          Number(details.cargo_value || details.cargoValue) || undefined;
        const isFreightDeduction = Boolean(
          details.is_freight_deduction || details.isFreightDeduction
        );
        const isValueCompensation = Boolean(
          details.is_value_compensation || details.isValueCompensation
        );
        const freightDeductionPercentage =
          Number(
            details.freight_deduction_percentage ||
              details.freightDeductionPercentage
          ) || undefined;
        const valueCompensationPercentage =
          Number(
            details.value_compensation_percentage ||
              details.valueCompensationPercentage
          ) || undefined;
        const proofImageUrls = Array.isArray(
          details.proof_of_value_image_urls || details.proofOfValueImageUrls
        )
          ? ((details.proof_of_value_image_urls ||
              details.proofOfValueImageUrls) as string[])
          : [];

        return (
          <div>
            {/* 基本信息 */}
            {cargoValue && (
              <div style={{ fontSize: "12px", marginBottom: "2px" }}>
                货值: ¥{cargoValue}
              </div>
            )}
            {isFreightDeduction && (
              <div style={{ fontSize: "12px", marginBottom: "2px" }}>
                运费扣除: {freightDeductionPercentage}%
              </div>
            )}
            {isValueCompensation && (
              <div style={{ fontSize: "12px", marginBottom: "4px" }}>
                货值赔偿: {valueCompensationPercentage}%
              </div>
            )}

            {/* 图片预览 */}
            {Array.isArray(proofImageUrls) && proofImageUrls.length > 0 && (
              <div>
                <div
                  style={{
                    fontSize: "12px",
                    color: "#666",
                    marginBottom: "4px",
                  }}
                >
                  货值证明图片:
                </div>
                <div style={{ display: "flex", flexWrap: "wrap", gap: "4px" }}>
                  {proofImageUrls
                    .slice(0, 3)
                    .map((url: string, index: number) => (
                      <div
                        key={index}
                        style={{
                          width: "40px",
                          height: "40px",
                          border: "1px solid #d9d9d9",
                          borderRadius: "4px",
                          overflow: "hidden",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <Image
                          src={url}
                          alt={`货值证明图片${index + 1}`}
                          style={{
                            width: "100%",
                            height: "100%",
                            objectFit: "cover",
                          }}
                          preview={{
                            mask: (
                              <EyeOutlined
                                style={{ color: "#fff", fontSize: "12px" }}
                              />
                            ),
                          }}
                          fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAxNkMyMS4xMDQ2IDE2IDIyIDE2Ljg5NTQgMjIgMThDMjIgMTkuMTA0NiAyMS4xMDQ2IDIwIDIwIDIwQzE4Ljg5NTQgMjAgMTggMTkuMTA0NiAxOCAxOEMxOCAxNi44OTU0IDE4Ljg5NTQgMTYgMjAgMTZaIiBmaWxsPSIjQkZCRkJGIi8+CjxwYXRoIGQ9Ik0xNCAyNEwyMCAyMEwyNiAyNEgxNFoiIGZpbGw9IiNCRkJGQkYiLz4KPHN2Zz4K"
                        />
                      </div>
                    ))}
                  {proofImageUrls.length > 3 && (
                    <div
                      style={{
                        width: "40px",
                        height: "40px",
                        border: "1px solid #d9d9d9",
                        borderRadius: "4px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: "10px",
                        color: "#666",
                        backgroundColor: "#fafafa",
                      }}
                    >
                      +{proofImageUrls.length - 3}
                    </div>
                  )}
                </div>
                <div
                  style={{ fontSize: "10px", color: "#999", marginTop: "2px" }}
                >
                  共 {proofImageUrls.length} 张，点击预览
                </div>
              </div>
            )}

            {/* 无详情提示 */}
            {!cargoValue &&
              !isFreightDeduction &&
              !isValueCompensation &&
              proofImageUrls.length === 0 && (
                <Text type="secondary" style={{ fontSize: "12px" }}>
                  暂无详细信息
                </Text>
              )}
          </div>
        );
      },
    },
    {
      title: "运单信息",
      key: "manifest",
      width: 220,
      render: (_, record) => renderManifestInfo(record),
    },
    {
      title: "收件人",
      dataIndex: "manifestReceiverName",
      width: 120,
      ellipsis: {
        showTitle: false,
      },
      render: (name: string) => {
        if (!name || name.trim() === "") {
          return <Text type="secondary">-</Text>;
        }
        return (
          <Tooltip title={name} placement="topLeft">
            <span>{name}</span>
          </Tooltip>
        );
      },
    },
    {
      title: "物品描述",
      dataIndex: "manifestItemDescription",
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (description: string) => {
        if (!description || description.trim() === "") {
          return <Text type="secondary">-</Text>;
        }
        return (
          <Tooltip title={description} placement="topLeft">
            <span>{description}</span>
          </Tooltip>
        );
      },
    },
    {
      title: "货物类型",
      dataIndex: "manifestCargoType",
      width: 100,
      align: "center",
      render: (cargoType: string) => {
        if (!cargoType || cargoType.trim() === "") {
          return <Text type="secondary">-</Text>;
        }

        // 根据货物类型设置不同颜色
        const getColor = (type: string) => {
          if (type.includes("普通")) return "blue";
          if (type.includes("带电")) return "orange";
          if (type.includes("投函")) return "green";
          return "default";
        };

        return <Tag color={getColor(cargoType)}>{cargoType}</Tag>;
      },
    },
    {
      title: "重量信息",
      key: "weight",
      width: 140,
      render: (_, record) => {
        const hasWeight = isValidPositiveNumber(record.manifestWeight);
        const hasChargeableWeight = isValidPositiveNumber(
          record.manifestChargeableWeight
        );

        return (
          <div>
            {hasWeight && (
              <div style={{ fontSize: "12px", marginBottom: "1px" }}>
                实重: {Number(record.manifestWeight).toFixed(3)} kg
              </div>
            )}
            {hasChargeableWeight && (
              <div
                style={{
                  fontSize: "12px",
                  fontWeight: "bold",
                  color: "#1890ff",
                }}
              >
                计费重: {Number(record.manifestChargeableWeight).toFixed(3)} kg
              </div>
            )}
            {!hasWeight && !hasChargeableWeight && (
              <Text type="secondary">-</Text>
            )}
          </div>
        );
      },
    },
    {
      title: "时间信息",
      key: "times",
      width: 160,
      render: (_, record) => (
        <div style={{ fontSize: "12px" }}>
          {record.manifestCreateTime && (
            <div style={{ marginBottom: "2px" }}>
              <span style={{ color: "#666" }}>创建:</span>{" "}
              <span>
                {dayjs(record.manifestCreateTime).format("MM-DD HH:mm")}
              </span>
            </div>
          )}
          {record.manifestShipmentTime && (
            <div style={{ marginBottom: "2px" }}>
              <span style={{ color: "#666" }}>发货:</span>{" "}
              <span>
                {dayjs(record.manifestShipmentTime).format("MM-DD HH:mm")}
              </span>
            </div>
          )}
          <div>
            <span style={{ color: "#666" }}>快照:</span>{" "}
            <span>{dayjs(record.snapshotTime).format("MM-DD HH:mm")}</span>
          </div>
        </div>
      ),
    },
  ];

  // 初始化数据
  useEffect(() => {
    if (visible && billingRecordId && !isNaN(billingRecordId)) {
      setCurrentPage(1);
      setPageSize(10);
      loadAdjustmentData(1, 10);
    }
  }, [visible, billingRecordId, loadAdjustmentData]);

  return (
    <Modal
      title={
        <Space>
          <FileTextOutlined />
          <span>运费调整记录</span>
          <Text type="secondary">({billNumber})</Text>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <Button
          key="refresh"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={loading}
        >
          刷新
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
      destroyOnClose
    >
      {/* 调整记录表格 */}
      <Table
        columns={columns}
        dataSource={adjustmentData.list}
        rowKey="id"
        loading={loading}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: adjustmentData.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条调整记录`,
          pageSizeOptions: ["10", "20", "50"],
          onChange: handleTableChange,
          onShowSizeChange: handleTableChange,
        }}
        scroll={{ x: 1000, y: 400 }}
        size="small"
        bordered
        locale={{
          emptyText: (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无调整记录"
            />
          ),
        }}
      />
    </Modal>
  );
};

export default BillingAdjustmentModal;
