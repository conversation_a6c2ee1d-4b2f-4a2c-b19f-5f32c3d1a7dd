import { apiClient } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 账单批次状态枚举
export enum BillingCycleStatus {
  PENDING = "PENDING", // 待处理
  PROCESSING = "PROCESSING", // 处理中
  COMPLETED = "COMPLETED", // 已完成
  FAILED = "FAILED", // 失败
  CANCELLED = "CANCELLED", // 已取消
}

// 账单批次接口
export interface BillingCycle {
  id: number; // 账单批次ID
  cycleYear: number; // 账单批次年份
  cycleMonth: number; // 账单批次月份 (1-12)
  cycleName: string; // 账单批次名称
  status: BillingCycleStatus; // 账单批次状态
  statusName: string; // 账单批次状态名称
  totalCustomersBilled?: number; // 本周期内出账客户数
  totalBillsGenerated?: number; // 本周期内生成的账单总数
  totalBilledAmount?: number; // 本周期内账单总金额合计
  totalAmountPaidInCycle?: number; // 本周期内已付金额合计
  totalBalanceDueInCycle?: number; // 本周期内待支付金额合计
  generatedByUserId?: number; // 批次生成操作员ID
  generatedByNickname?: string; // 批次生成操作员昵称
  generationStartTime?: string; // 批次账单开始生成时间
  generationEndTime?: string; // 批次账单完成生成时间
  notes?: string; // 批次备注
  createTime: string; // 创建时间
  updateTime: string; // 更新时间
}

// 账单批次查询参数接口
export interface BillingCycleQueryParams {
  page?: number; // 页码，默认1
  pageSize?: number; // 每页数量，默认10，最大100
  cycleYear?: number; // 账单批次年份
  cycleMonth?: number; // 账单批次月份
  status?: BillingCycleStatus; // 账单批次状态
  cycleName?: string; // 账单批次名称模糊查询
  generatedByUserId?: number; // 生成操作员ID
}

// 账单批次分页数据接口
export interface BillingCyclePageData {
  total: number; // 总记录数
  list: BillingCycle[]; // 账单批次列表
}

// 账单批次响应接口
export interface BillingCycleResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: BillingCyclePageData;
}

// 创建账单批次请求参数接口
export interface CreateBillingCycleRequest {
  cycleYear: number; // 必填，账单批次年份 (2020-2100)
  cycleMonth: number; // 必填，账单批次月份 (1-12)
  cycleName?: string; // 可选，账单批次名称（不填则自动生成）
  notes?: string; // 可选，批次备注
}

// 创建账单批次响应数据
export interface CreateBillingCycleData {
  billingCycleId: number; // 生成的账单批次ID
  cycleName: string; // 账单批次名称
  message: string; // 响应消息
}

// 创建账单批次响应接口
export interface CreateBillingCycleResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: CreateBillingCycleData;
}

// 账单批次详情响应接口
export interface BillingCycleDetailResponse {
  success: boolean;
  errorCode: number;
  errorMessage: string;
  requestId: string;
  timestamp: string;
  data: {
    billingCycle: BillingCycle;
  };
}

/**
 * 分页查询账单批次列表
 * @param params 查询参数
 * @returns 账单批次分页数据
 */
export const fetchBillingCycles = async (
  params: BillingCycleQueryParams = {}
): Promise<BillingCyclePageData> => {
  const response = await apiClient.get<BillingCycleResponse>(
    getApiPath("/billing/cycles"),
    { params }
  );

  return response.data.data;
};

/**
 * 创建账单批次
 * @param params 创建参数
 * @returns 创建结果
 */
export const createBillingCycle = async (
  params: CreateBillingCycleRequest
): Promise<CreateBillingCycleData> => {
  // apiClient的拦截器已经处理了success: false的情况，这里不需要额外检查
  const response = await apiClient.post<CreateBillingCycleResponse>(
    getApiPath("/billing/cycles"),
    params
  );

  return response.data.data;
};

/**
 * 获取账单批次详情
 * @param billingCycleId 账单批次ID
 * @returns 账单批次详情
 */
export const fetchBillingCycleDetail = async (
  billingCycleId: number
): Promise<BillingCycle> => {
  const response = await apiClient.get<BillingCycleDetailResponse>(
    getApiPath(`/billing/cycles/${billingCycleId}`)
  );

  return response.data.data.billingCycle;
};

// 账单批次状态选项，用于筛选下拉框
export const BILLING_CYCLE_STATUS_OPTIONS = [
  { value: BillingCycleStatus.PENDING, label: "待处理" },
  { value: BillingCycleStatus.PROCESSING, label: "处理中" },
  { value: BillingCycleStatus.COMPLETED, label: "已完成" },
  { value: BillingCycleStatus.FAILED, label: "失败" },
  { value: BillingCycleStatus.CANCELLED, label: "已取消" },
];

/**
 * 格式化账单批次状态为中文显示
 * @param status 账单批次状态
 * @returns 中文状态描述
 */
export const formatBillingCycleStatus = (
  status: BillingCycleStatus
): string => {
  const statusMap = {
    [BillingCycleStatus.PENDING]: "待处理",
    [BillingCycleStatus.PROCESSING]: "处理中",
    [BillingCycleStatus.COMPLETED]: "已完成",
    [BillingCycleStatus.FAILED]: "失败",
    [BillingCycleStatus.CANCELLED]: "已取消",
  };
  return statusMap[status] || "未知状态";
};

// 月份选项
export const MONTH_OPTIONS = [
  { value: 1, label: "1月" },
  { value: 2, label: "2月" },
  { value: 3, label: "3月" },
  { value: 4, label: "4月" },
  { value: 5, label: "5月" },
  { value: 6, label: "6月" },
  { value: 7, label: "7月" },
  { value: 8, label: "8月" },
  { value: 9, label: "9月" },
  { value: 10, label: "10月" },
  { value: 11, label: "11月" },
  { value: 12, label: "12月" },
];

/**
 * 生成年份选项（当前年份前后一年）
 * @returns 年份选项数组
 */
export const generateYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  for (let i = currentYear - 1; i <= currentYear + 1; i++) {
    years.push({ value: i, label: `${i}年` });
  }
  return years;
};
