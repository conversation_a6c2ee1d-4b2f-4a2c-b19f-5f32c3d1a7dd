.login-greeting {
  text-align: center;
  margin-bottom: 12px !important; /* 增加与下方文字的间距 */
  font-weight: 600;
  font-size: 1.8rem !important;
  color: #1e3a8a !important;
  letter-spacing: 0.5px;
}

.login-system-name {
  display: block;
  text-align: center;
  margin-bottom: 42px !important; /* 增加与输入框的间距 */
  color: #6b7280; /* 浅灰色 */
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.login-form {
  width: 100%;
}

/* 输入框基础样式 */
.login-form .ant-input-affix-wrapper {
  border-radius: 10px; /* 增加圆角 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  padding: 12px 14px; /* 增加内边距 */
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.19, 1, 0.22, 1); /* 使用更平滑的动画 */
  margin-bottom: 20px; /* 输入框之间的间距 */
}

/* 覆盖浏览器自动填充样式 */
.login-form .ant-input-affix-wrapper input:-webkit-autofill,
.login-form .ant-input-affix-wrapper input:-webkit-autofill:hover,
.login-form .ant-input-affix-wrapper input:-webkit-autofill:focus,
.login-form .ant-input-affix-wrapper input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important; /* 使用白色背景填充 */
  box-shadow: 0 0 0 30px white inset !important;
  background-color: white !important; /* 确保背景色是白色 */
  -webkit-text-fill-color: inherit !important; /* 保持文字颜色 */
}

/* 输入框焦点动效 */
.login-form .ant-input-affix-wrapper:focus,
.login-form .ant-input-affix-wrapper-focused {
  transform: scale(1.02) translateY(-2px); /* 增加细微的上移效果 */
  box-shadow: 0 8px 15px rgba(59, 130, 246, 0.15); /* 增强阴影效果 */
  border-color: #3b82f6;
}

/* 输入框悬停效果 */
.login-form .ant-input-affix-wrapper:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 10px rgba(59, 130, 246, 0.1);
}

/* 输入框内图标样式 */
.login-form .ant-input-prefix {
  color: #3b82f6;
  margin-right: 12px; /* 增加图标与文本间距 */
  font-size: 18px; /* 增大图标尺寸 */
  display: flex; /* 确保图标垂直居中 */
  align-items: center;
}

/* 记住登录信息行样式 */
.login-form .ant-row {
  margin-bottom: 25px; /* 增加与按钮的间距 */
}

.login-form-forgot {
  float: right;
  color: #3b82f6 !important;
  text-decoration: none;
  transition: color 0.3s ease;
  font-weight: 500; /* 加粗一点 */
}

.login-form-forgot:hover {
  color: #1e3a8a !important;
  text-decoration: underline;
}

/* 自定义复选框样式 */
.login-form .ant-checkbox-checked .ant-checkbox-inner {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.login-form .ant-checkbox-inner {
  border-radius: 4px;
  border-width: 1.5px; /* 加粗边框 */
  transition: all 0.3s ease;
}

.login-form .ant-checkbox-wrapper:hover .ant-checkbox-inner {
  border-color: #3b82f6;
}

.login-form-button {
  width: 100%;
  height: 50px; /* 增加按钮高度 */
  font-size: 1.05rem; /* 增大文本 */
  font-weight: 500;
  letter-spacing: 1.2px; /* 增加字间距 */
  /* 调整为更具层次感的蓝色渐变 */
  background: linear-gradient(135deg, #60a5fa, #3b82f6, #1e40af) !important;
  border: none !important;
  border-radius: 12px !important; /* 增加圆角 */
  transition: all 0.4s cubic-bezier(0.19, 1, 0.22, 1) !important; /* 使用更丰富的动画曲线 */
  box-shadow: 0 6px 12px rgba(59, 130, 246, 0.25),
    0 0 0 3px rgba(59, 130, 246, 0.05) !important;
  position: relative;
  overflow: hidden;
}

.login-form-button:hover {
  transform: translateY(-3px) scale(1.015); /* 上移更明显 */
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.35),
    0 0 0 3px rgba(59, 130, 246, 0.08) !important;
  background: linear-gradient(
    135deg,
    #3b82f6,
    #1e40af,
    #1e3a8a
  ) !important; /* 悬停时加深渐变 */
}

.login-form-button:active {
  transform: translateY(1px) scale(0.99); /* 按下时缩小效果 */
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2) !important;
}
