import { apiClient, ApiResponse } from "./apiClient";
import { getApiPath } from "./apiPaths";

// 错误码常量定义
export const ERROR_CODES = {
  ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID: 201009,
  ERROR_MANIFEST_ZIPCODE_NOT_EXIST: 201010,
  ERROR_MANIFEST_PHONE_FORMAT_INVALID: 201011,
};

// 物品信息接口
export interface ManifestItem {
  weight: number;
  quantity: number;
  price: number;
  name: string;
}

// 预报单接口
export interface ManifestInfo {
  id: number;
  expressNumber: string;
  orderNumber: string;
  receiverZipCode: string;
  receiverName: string;
  receiverAddress: string;
  receiverPhone: string;
  userNickname: string;
  items: ManifestItem[];
  validateStatus: number; // 验证状态： 0-未验证，1-验证成功，2-验证失败
  validateError: string | null; // 验证错误信息
  createTime: string;
}

// 预报列表响应数据
export interface ManifestListData {
  total: number;
  list: ManifestInfo[];
}

// 查询参数接口
export interface ManifestQueryParams {
  page?: number;
  pageSize?: number;
  expressNumber?: string;
  orderNumber?: string;
  validateStatus?: number;
  startTime?: string;
  endTime?: string;
}

/**
 * 获取待审核的预报列表
 * @param params 查询参数
 * @returns 预报列表数据
 */
export const getPendingManifests = async (
  params?: ManifestQueryParams
): Promise<ManifestListData> => {
  // 添加调试代码，查看令牌是否存在
  const token = localStorage.getItem("authToken");
  console.log(
    "发送预报查询前检查Token:",
    token ? "Token存在" : "Token不存在",
    token ? token.substring(0, 10) + "..." : ""
  );

  // 也可以手动设置请求头进行测试
  const headers = token ? { Authorization: `Bearer ${token}` } : undefined;

  const response = await apiClient.get<ApiResponse<ManifestListData>>(
    getApiPath("/manifests/pending-review"),
    {
      params,
      headers, // 手动添加请求头，这实际上是冗余的，因为拦截器也会添加
    }
  );

  // 打印请求的详细信息
  console.log("预报API请求详情:", {
    url: getApiPath("/manifests/pending-review"),
    headers: response.config.headers, // 查看最终发送的请求头
    params: response.config.params,
  });

  return response.data.data || { total: 0, list: [] };
};

/**
 * 更新预报单信息
 * @param id 预报单ID
 * @param data 更新的预报单数据
 * @returns 更新结果
 */
export const updateManifest = async (
  id: number,
  data: Partial<ManifestInfo>
): Promise<{ id: number }> => {
  // 构建请求数据，确保符合API要求的格式
  const requestData = {
    id,
    receiverName: data.receiverName,
    receiverPhone: data.receiverPhone,
    receiverZipCode: data.receiverZipCode,
    receiverAddress: data.receiverAddress,
    items: data.items?.map((item) => ({
      name: item.name,
      weight: item.weight,
      quantity: item.quantity,
      price: item.price,
      value: item.price * item.quantity,
    })),
  };

  const response = await apiClient.post<ApiResponse<{ id: number }>>(
    getApiPath(`/manifests/update`),
    requestData
  );
  return response.data.data || { id };
};

/**
 * 通过预报审核
 * @param id 预报单ID
 * @returns 审核结果
 */
export const approveManifest = async (id: number): Promise<boolean> => {
  const response = await apiClient.post<ApiResponse<boolean>>(
    getApiPath(`/manifests/approve`),
    { id }
  );
  return !!response.data.data;
};

/**
 * 驳回预报
 * @param id 预报单ID
 * @param reason 驳回原因
 * @returns 驳回结果
 */
export const rejectManifest = async (
  id: number,
  reason: string
): Promise<boolean> => {
  const response = await apiClient.post<ApiResponse<boolean>>(
    getApiPath(`/manifests/${id}/reject`),
    { reason }
  );
  return !!response.data.data;
};

/**
 * 获取待审核的预报数量
 * @returns 待审核的预报数量
 */
export const getPendingManifestCount = async (): Promise<number> => {
  const response = await apiClient.get<ApiResponse<{ count: number }>>(
    getApiPath("/manifests/pending-count")
  );
  return response.data.data?.count || 0;
};
