---
description: 
globs: 
alwaysApply: true
---
# Antd Message 组件使用规范

## 🚫 错误的导入方式
```typescript
// ❌ 不要这样导入和使用
import { message } from "antd";

const MyComponent = () => {
  const handleClick = () => {
    message.success("操作成功"); // 这样可能不会显示
  };
  // ...
};
```

## ✅ 正确的导入和使用方式
```typescript
// ✅ 正确的导入方式
import { App } from "antd";

const MyComponent = () => {
  const { message } = App.useApp(); // 使用useApp钩子获取message实例
  
  const handleClick = () => {
    message.success("操作成功"); // 这样可以正常显示
  };
  // ...
};
```

## 📋 项目架构说明

项目使用了全局message实例架构：

1. **全局配置**: 在 [App.tsx](mdc:src/App.tsx) 中通过 `AntdApp.useApp()` 获取message实例
2. **全局注册**: 通过 `setGlobalMessageInstance(message)` 注册到 [apiClient.ts](mdc:src/services/apiClient.ts)
3. **组件使用**: 在组件中必须使用 `App.useApp()` 获取message实例

## 🔧 技术原理

- 项目使用了 `<AntdApp>` 包装器提供统一的message实例
- 直接从antd导入的message可能无法正常工作
- apiClient中的错误处理也依赖这个全局message实例

## 📝 开发注意事项

1. **新组件开发**: 始终使用 `App.useApp()` 获取message实例
2. **代码审查**: 检查是否有直接导入message的情况
3. **错误排查**: 如果message不显示，首先检查导入方式是否正确

## 🌟 参考示例

参考 [UserTemplateConfigPage.tsx](mdc:src/pages/ShippingFeeManagement/UserTemplateConfigPage.tsx) 中的正确实现方式。

