import React from "react";
// import { createFromIconfontCN } from "@ant-design/icons"; // 注释掉未使用的导入

// 如果有自己的 iconfont 项目，可以在这里替换 URL
/* // 注释掉未使用的变量定义
const IconFont = createFromIconfontCN({
  scriptUrl: "//at.alicdn.com/t/c/font_8d5l8fzk5b8.js", // 示例 URL，可能需要替换
});
*/

// 假设你的 iconfont 项目中有一个名为 'icon-zebra' 的图标
// 或者你可以直接使用 SVG
const ZebraIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  // 这是一个简化的 SVG 示例，你需要替换成设计稿中的简化斑马图案
  <svg
    viewBox="0 0 1024 1024"
    focusable="false"
    data-icon="zebra"
    width="1em"
    height="1em"
    fill="currentColor"
    aria-hidden="true"
    {...props}
  >
    {/* 使用一个更像斑马的简化图标，这里用日历图标暂代 */}
    <path d="M864 170h-64v-32c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v32H288v-32c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v32H160c-17.7 0-32 14.3-32 32v704c0 17.7 14.3 32 32 32h704c17.7 0 32-14.3 32-32V202c0-17.7-14.3-32-32-32zm-40 724H184V291.4h640V894z"></path>
    <path d="M360 508h304c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm0 152h152c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H360c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8z"></path>
  </svg>
  // 如果使用 iconfont:
  // <IconFont type="icon-zebra" />
);

export default ZebraIcon;
