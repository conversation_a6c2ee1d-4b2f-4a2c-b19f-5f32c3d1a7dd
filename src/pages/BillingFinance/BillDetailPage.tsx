import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Card,
  Descriptions,
  Table,
  Button,
  Space,
  Typography,
  Tag,
  Divider,
  Row,
  Col,
  Statistic,
  Tooltip,
  Spin,
  message,
  Badge,
  Alert,
  Empty,
} from "antd";
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  UserOutlined,
  CalendarOutlined,
  DollarOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  ReloadOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
  fetchBillingRecordDetail,
  fetchBillingRecordItems,
  BillingRecordDetail,
  BillingRecordItem,
  BillingRecordItemsPageData,
  formatCargoType,
  ShippingFeeTemplate,
  ShippingTemplateType,
  downloadBillingRecord,
} from "../../services/billingService";
import BillingAdjustmentModal from "./BillingAdjustmentModal";

const { Text } = Typography;

const BillDetailPage: React.FC = () => {
  const { billId, cycleId } = useParams<{ billId: string; cycleId?: string }>();
  const navigate = useNavigate();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [itemsLoading, setItemsLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false); // 导出加载状态
  const [billingDetail, setBillingDetail] =
    useState<BillingRecordDetail | null>(null);
  const [itemsData, setItemsData] = useState<BillingRecordItemsPageData>({
    total: 0,
    list: [],
  });

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 调整记录弹窗状态
  const [adjustmentModalVisible, setAdjustmentModalVisible] = useState(false);

  // 账单记录ID
  const billingRecordId = Number(billId);

  /**
   * 获取账单详情
   */
  const loadBillingDetail = async () => {
    if (!billingRecordId || isNaN(billingRecordId)) {
      message.error("无效的账单记录ID");
      return;
    }

    try {
      setLoading(true);
      const detail = await fetchBillingRecordDetail(billingRecordId);
      setBillingDetail(detail);
    } catch (error) {
      console.error("获取账单详情失败:", error);
      message.error("获取账单详情失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取账单明细列表
   */
  const loadBillingItems = async (
    page: number = currentPage,
    size: number = pageSize
  ) => {
    if (!billingRecordId || isNaN(billingRecordId)) {
      return;
    }

    try {
      setItemsLoading(true);
      const data = await fetchBillingRecordItems(billingRecordId, {
        page,
        pageSize: size,
      });
      setItemsData(data);
    } catch (error) {
      console.error("获取账单明细失败:", error);
      message.error("获取账单明细失败，请重试");
    } finally {
      setItemsLoading(false);
    }
  };

  /**
   * 处理分页变化
   */
  const handleTableChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    loadBillingItems(page, size);
  };

  /**
   * 刷新数据
   */
  const handleRefresh = () => {
    loadBillingDetail();
    loadBillingItems();
  };

  /**
   * 返回列表页
   */
  const handleBack = () => {
    if (cycleId) {
      // 如果有cycleId，返回到特定账期批次的账单列表
      navigate(`/billing-finance/cycles/${cycleId}/bills`);
    } else {
      // 否则返回到独立的账单管理页面
      navigate("/billing-finance/bills");
    }
  };

  /**
   * 打开调整记录弹窗
   */
  const handleOpenAdjustmentModal = () => {
    setAdjustmentModalVisible(true);
  };

  /**
   * 关闭调整记录弹窗
   */
  const handleCloseAdjustmentModal = () => {
    setAdjustmentModalVisible(false);
  };

  /**
   * 导出账单Excel
   */
  const handleExportBilling = async () => {
    if (!billingRecordId || isNaN(billingRecordId)) {
      message.error("无效的账单记录ID");
      return;
    }

    try {
      setExportLoading(true);
      await downloadBillingRecord(billingRecordId);
      message.success("账单导出成功");
    } catch (error) {
      console.error("导出账单失败:", error);
      message.error(
        error instanceof Error ? error.message : "导出账单失败，请重试"
      );
    } finally {
      setExportLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    if (billingRecordId && !isNaN(billingRecordId)) {
      loadBillingDetail();
      loadBillingItems();
    }
  }, [billingRecordId]);

  /**
   * 渲染账单状态标签
   */
  const renderStatusTag = (status: string, statusName: string) => {
    const statusColorMap: Record<string, string> = {
      UNPAID: "orange",
      PAID: "green",
      PARTIALLY_PAID: "blue",
      OVERDUE: "red",
      VOID: "default",
    };

    return (
      <Tag
        color={statusColorMap[status] || "default"}
        icon={<InfoCircleOutlined />}
      >
        {statusName}
      </Tag>
    );
  };

  /**
   * 渲染运费模板信息
   */
  const renderTemplateInfo = (
    template: ShippingFeeTemplate | undefined,
    type: ShippingTemplateType,
    typeName: string
  ) => {
    if (!template) {
      return (
        <Card size="small" style={{ marginBottom: "12px" }}>
          <div style={{ color: "#999", textAlign: "center" }}>
            {typeName}: 未配置
          </div>
        </Card>
      );
    }

    return (
      <Card size="small" style={{ marginBottom: "12px" }}>
        <div style={{ fontWeight: "bold", marginBottom: "8px" }}>
          {typeName}
        </div>
        <Row gutter={16}>
          <Col span={8}>
            <Text>首重价格: {template.firstWeightPrice} 元</Text>
          </Col>
          <Col span={8}>
            <Text>首重范围: {template.firstWeightRange} 公斤</Text>
          </Col>
          <Col span={8}>
            <Text>续重价格: {template.continuedWeightPrice} 元</Text>
          </Col>
          <Col span={8}>
            <Text>续重区间: {template.continuedWeightInterval} 公斤</Text>
          </Col>
          <Col span={8}>
            <Text>轻抛系数: {template.bulkCoefficient}</Text>
          </Col>
          <Col span={8}>
            <Text>三边和阈值: {template.threeSidesStart} 厘米</Text>
          </Col>
        </Row>
      </Card>
    );
  };

  /**
   * 检查数值是否有效且大于0
   */
  const isValidPositiveNumber = (
    value: number | string | null | undefined
  ): boolean => {
    if (value == null || value === "" || value === "0" || value === 0) {
      return false;
    }
    const num = Number(value);
    return !isNaN(num) && num > 0;
  };

  /**
   * 明细表格列定义
   */
  const itemColumns: ColumnsType<BillingRecordItem> = [
    {
      title: "运单信息",
      key: "manifest",
      width: 220,
      render: (_, record) => {
        const hasExpressNumber =
          record.expressNumber && record.expressNumber.trim() !== "";
        const hasOrderNo = record.orderNo && record.orderNo.trim() !== "";
        const hasTransferredTrackingNumber =
          record.transferredTrackingNumber &&
          record.transferredTrackingNumber.trim() !== "";
        const hasOrderNumber =
          record.orderNumber && record.orderNumber.trim() !== "";

        return (
          <div>
            {hasExpressNumber && (
              <div style={{ fontWeight: "bold", marginBottom: "4px" }}>
                <Tooltip title="快递单号">
                  <span style={{ fontFamily: "monospace" }}>
                    {record.expressNumber}
                  </span>
                </Tooltip>
              </div>
            )}
            {hasOrderNo && (
              <div
                style={{ fontSize: "12px", color: "#666", marginBottom: "2px" }}
              >
                系统订单: {record.orderNo}
              </div>
            )}
            {hasTransferredTrackingNumber && (
              <div
                style={{ fontSize: "12px", color: "#666", marginBottom: "2px" }}
              >
                转单号: {record.transferredTrackingNumber}
              </div>
            )}
            {hasOrderNumber && (
              <div style={{ fontSize: "12px", color: "#666" }}>
                客户订单: {record.orderNumber}
              </div>
            )}
            {!hasExpressNumber &&
              !hasOrderNo &&
              !hasTransferredTrackingNumber &&
              !hasOrderNumber && <Text type="secondary">暂无运单信息</Text>}
          </div>
        );
      },
    },
    {
      title: "收件人",
      dataIndex: "receiverName",
      width: 120,
      ellipsis: {
        showTitle: false,
      },
      render: (name: string) => {
        if (!name || name.trim() === "") {
          return <Text type="secondary">-</Text>;
        }
        return (
          <Tooltip title={name} placement="topLeft">
            <span>{name}</span>
          </Tooltip>
        );
      },
    },
    {
      title: "物品描述",
      dataIndex: "itemDescription",
      width: 160,
      ellipsis: {
        showTitle: false,
      },
      render: (description: string) => {
        const hasDescription = description && description.trim() !== "";

        if (!hasDescription) {
          return <Text type="secondary">-</Text>;
        }

        return (
          <Tooltip title={description} placement="topLeft">
            <span>{description}</span>
          </Tooltip>
        );
      },
    },
    {
      title: "货物类型",
      dataIndex: "cargoType",
      width: 100,
      align: "center",
      render: (type: number, record) => {
        // 货物类型颜色映射
        const getTypeColor = (cargoType: number) => {
          switch (cargoType) {
            case 1:
              return "blue"; // 普通货物
            case 2:
              return "orange"; // 带电货物
            case 3:
              return "green"; // 投函货物
            case 6:
              return "purple"; // 特殊货物
            default:
              return "default";
          }
        };

        return (
          <Tag color={getTypeColor(type)}>
            {record.cargoTypeName || formatCargoType(type)}
          </Tag>
        );
      },
    },
    {
      title: "重量信息",
      key: "weight",
      width: 160,
      render: (_, record) => {
        // 严格检查重量值是否存在且大于0
        const hasWeight = isValidPositiveNumber(record.weight);
        const hasDimensionalWeight = isValidPositiveNumber(
          record.dimensionalWeight
        );
        const hasChargeableWeight = isValidPositiveNumber(
          record.chargeableWeight
        );

        return (
          <div>
            {hasWeight && (
              <div style={{ marginBottom: "2px" }}>
                实重:{" "}
                <span style={{ fontWeight: "500" }}>
                  {Number(record.weight).toFixed(3)} kg
                </span>
              </div>
            )}
            {hasDimensionalWeight && (
              <div style={{ marginBottom: "2px" }}>
                体积重:{" "}
                <span style={{ fontWeight: "500" }}>
                  {Number(record.dimensionalWeight).toFixed(3)} kg
                </span>
              </div>
            )}
            {hasChargeableWeight && (
              <div style={{ fontWeight: "bold", color: "#1890ff" }}>
                计费重: {Number(record.chargeableWeight).toFixed(3)} kg
              </div>
            )}
            {!hasWeight && !hasDimensionalWeight && !hasChargeableWeight && (
              <Text type="secondary">暂无重量信息</Text>
            )}
          </div>
        );
      },
    },
    {
      title: "尺寸信息",
      key: "dimensions",
      width: 140,
      render: (_, record) => {
        // 严格检查尺寸值是否存在且大于0
        const hasLength = isValidPositiveNumber(record.length);
        const hasWidth = isValidPositiveNumber(record.width);
        const hasHeight = isValidPositiveNumber(record.height);
        const hasSumOfSides = isValidPositiveNumber(record.sumOfSides);

        return (
          <div>
            {hasLength && hasWidth && hasHeight && (
              <div style={{ marginBottom: "2px" }}>
                <Tooltip title="长×宽×高">
                  <span>
                    {Number(record.length).toFixed(1)}×
                    {Number(record.width).toFixed(1)}×
                    {Number(record.height).toFixed(1)} cm
                  </span>
                </Tooltip>
              </div>
            )}
            {hasSumOfSides && (
              <div style={{ fontSize: "12px", color: "#666" }}>
                三边和: {Number(record.sumOfSides).toFixed(1)} cm
              </div>
            )}
            {!hasLength && !hasSumOfSides && <Text type="secondary">-</Text>}
          </div>
        );
      },
    },
    {
      title: "费用明细",
      key: "fees",
      width: 200,
      render: (_, record) => {
        // 严格检查各种费用是否存在且大于0
        const hasBaseFreightFee = isValidPositiveNumber(record.baseFreightFee);
        const hasFirstWeightFee = isValidPositiveNumber(record.firstWeightFee);
        const hasContinuedWeightFee = isValidPositiveNumber(
          record.continuedWeightFee
        );
        const hasOverLengthSurcharge = isValidPositiveNumber(
          record.overLengthSurcharge
        );
        const hasOverweightSurcharge = isValidPositiveNumber(
          record.overweightSurcharge
        );
        const hasRemoteAreaSurcharge = isValidPositiveNumber(
          record.remoteAreaSurcharge
        );

        // 检查是否有任何费用项
        const hasAnyFee =
          hasBaseFreightFee ||
          hasFirstWeightFee ||
          hasContinuedWeightFee ||
          hasOverLengthSurcharge ||
          hasOverweightSurcharge ||
          hasRemoteAreaSurcharge;

        return (
          <div>
            {hasBaseFreightFee && (
              <div style={{ marginBottom: "2px" }}>
                基础运费:{" "}
                <span style={{ color: "#52c41a" }}>
                  {Number(record.baseFreightFee).toFixed(2)} 元
                </span>
              </div>
            )}
            {hasFirstWeightFee && (
              <div style={{ marginBottom: "2px" }}>
                首重费:{" "}
                <span style={{ color: "#1890ff" }}>
                  {Number(record.firstWeightFee).toFixed(2)} 元
                </span>
              </div>
            )}
            {hasContinuedWeightFee && (
              <div style={{ marginBottom: "2px" }}>
                续重费:{" "}
                <span style={{ color: "#722ed1" }}>
                  {Number(record.continuedWeightFee).toFixed(2)} 元
                </span>
              </div>
            )}
            {hasOverLengthSurcharge && (
              <div style={{ marginBottom: "2px" }}>
                超长费:{" "}
                <span style={{ color: "#fa8c16" }}>
                  {Number(record.overLengthSurcharge).toFixed(2)} 元
                </span>
              </div>
            )}
            {hasOverweightSurcharge && (
              <div style={{ marginBottom: "2px" }}>
                超重费:{" "}
                <span style={{ color: "#f5222d" }}>
                  {Number(record.overweightSurcharge).toFixed(2)} 元
                </span>
              </div>
            )}
            {hasRemoteAreaSurcharge && (
              <div style={{ marginBottom: "2px" }}>
                偏远费:{" "}
                <span style={{ color: "#eb2f96" }}>
                  {Number(record.remoteAreaSurcharge).toFixed(2)} 元
                </span>
              </div>
            )}

            {/* 如果没有任何费用明细，显示提示信息 */}
            {!hasAnyFee && (
              <div
                style={{ color: "#999", fontSize: "12px", marginBottom: "4px" }}
              >
                暂无费用明细
              </div>
            )}

            {/* 小计显示逻辑优化 */}
            {isValidPositiveNumber(record.itemTotalAmount) ? (
              <div
                style={{
                  fontWeight: "bold",
                  color: "#ff4d4f",
                  borderTop: hasAnyFee ? "1px solid #f0f0f0" : "none",
                  paddingTop: hasAnyFee ? "4px" : "0",
                  marginTop: hasAnyFee ? "4px" : "0",
                }}
              >
                小计: {Number(record.itemTotalAmount).toFixed(2)} 元
              </div>
            ) : (
              <div style={{ color: "#999", fontSize: "12px" }}>
                小计: 0.00 元
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: "时间信息",
      key: "times",
      width: 160,
      render: (_, record) => (
        <div style={{ fontSize: "12px" }}>
          {record.manifestCreateTime && (
            <div style={{ marginBottom: "2px" }}>
              <span style={{ color: "#666" }}>创建:</span>{" "}
              <span>
                {dayjs(record.manifestCreateTime).format("MM-DD HH:mm")}
              </span>
            </div>
          )}
          {record.shipmentTime && (
            <div>
              <span style={{ color: "#666" }}>发货:</span>{" "}
              <span>{dayjs(record.shipmentTime).format("MM-DD HH:mm")}</span>
            </div>
          )}
          {!record.manifestCreateTime && !record.shipmentTime && (
            <Text type="secondary">-</Text>
          )}
        </div>
      ),
    },
  ];

  if (!billingRecordId || isNaN(billingRecordId)) {
    return (
      <div style={{ padding: "24px" }}>
        <Alert
          message="错误"
          description="无效的账单记录ID"
          type="error"
          showIcon
          action={<Button onClick={handleBack}>返回列表</Button>}
        />
      </div>
    );
  }

  return (
    <div style={{ padding: "24px" }}>
      <style>
        {`
          .table-row-light {
            background-color: #fafafa;
          }
          .table-row-dark {
            background-color: #ffffff;
          }
          .table-row-light:hover,
          .table-row-dark:hover {
            background-color: #e6f7ff !important;
          }
        `}
      </style>
      {/* 页面头部 */}
      <div style={{ marginBottom: "24px" }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            type="default"
          >
            返回列表
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            type="default"
          >
            刷新数据
          </Button>
          {billingDetail && (
            <>
              <Button
                icon={<FileTextOutlined />}
                onClick={handleOpenAdjustmentModal}
                type="default"
              >
                查看运费调整记录
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExportBilling}
                type="primary"
                loading={exportLoading}
              >
                导出账单
              </Button>
            </>
          )}
        </Space>
      </div>

      <Spin spinning={loading}>
        {billingDetail ? (
          <>
            {/* 账单基本信息 */}
            <Card
              title={
                <Space>
                  <FileTextOutlined />
                  <span>账单详情</span>
                  {renderStatusTag(
                    billingDetail.status,
                    billingDetail.statusName
                  )}
                </Space>
              }
              style={{ marginBottom: "24px" }}
            >
              {/* 统计信息 */}
              <Row gutter={16} style={{ marginBottom: "24px" }}>
                <Col span={4}>
                  <Statistic
                    title="账单总金额"
                    value={billingDetail.totalAmount}
                    precision={2}
                    suffix="元"
                    prefix={<DollarOutlined />}
                    valueStyle={{ color: "#cf1322" }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="运费明细总费用"
                    value={billingDetail.freightChargesTotal}
                    precision={2}
                    suffix="元"
                    valueStyle={{ color: "#1890ff" }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="调整明细总费用"
                    value={billingDetail.adjustmentChargesTotal}
                    precision={2}
                    suffix="元"
                    valueStyle={{
                      color:
                        billingDetail.adjustmentChargesTotal >= 0
                          ? "#52c41a"
                          : "#f5222d",
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="已付金额"
                    value={billingDetail.amountPaid}
                    precision={2}
                    suffix="元"
                    valueStyle={{ color: "#389e0d" }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="应付余额"
                    value={billingDetail.balanceDue}
                    precision={2}
                    suffix="元"
                    valueStyle={{
                      color:
                        billingDetail.balanceDue > 0 ? "#d46b08" : "#389e0d",
                    }}
                  />
                </Col>
                <Col span={4}>
                  <Statistic
                    title="明细数量"
                    value={itemsData.total}
                    suffix="条"
                  />
                </Col>
              </Row>

              {/* 详细信息 */}
              <Descriptions bordered column={2} size="small">
                <Descriptions.Item label="账单编号" span={2}>
                  <Text copyable strong>
                    {billingDetail.billNumber}
                  </Text>
                </Descriptions.Item>
                <Descriptions.Item label="客户信息">
                  <Space>
                    <UserOutlined />
                    <span>{billingDetail.customerNickname}</span>
                    <Text type="secondary">
                      ({billingDetail.customerUsername})
                    </Text>
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="客户账户ID">
                  {billingDetail.customerAccountId}
                </Descriptions.Item>
                <Descriptions.Item label="账单日期">
                  <Space>
                    <CalendarOutlined />
                    {billingDetail.billDate}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="付款截止日期">
                  {billingDetail.dueDate ? (
                    <Space>
                      <CalendarOutlined />
                      <span
                        style={{
                          color: dayjs(billingDetail.dueDate).isBefore(dayjs())
                            ? "#ff4d4f"
                            : "inherit",
                        }}
                      >
                        {billingDetail.dueDate}
                      </span>
                      {dayjs(billingDetail.dueDate).isBefore(dayjs()) && (
                        <Badge status="error" text="已逾期" />
                      )}
                    </Space>
                  ) : (
                    <Text type="secondary">未设置</Text>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="账期时间">
                  {billingDetail.billingPeriodStart} ~{" "}
                  {billingDetail.billingPeriodEnd}
                </Descriptions.Item>
                <Descriptions.Item label="货币单位">
                  {billingDetail.currency}
                </Descriptions.Item>
                <Descriptions.Item label="支付方式">
                  {billingDetail.paymentMethod || (
                    <Text type="secondary">未支付</Text>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="支付交易号">
                  {billingDetail.paymentTransactionId ? (
                    <Text copyable>{billingDetail.paymentTransactionId}</Text>
                  ) : (
                    <Text type="secondary">无</Text>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="支付时间">
                  {billingDetail.paymentDate || (
                    <Text type="secondary">未支付</Text>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="生成操作员">
                  {billingDetail.generatedByNickname || (
                    <Text type="secondary">系统生成</Text>
                  )}
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {billingDetail.createTime}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {billingDetail.updateTime}
                </Descriptions.Item>
                <Descriptions.Item label="账单备注" span={2}>
                  {billingDetail.notes || <Text type="secondary">无备注</Text>}
                </Descriptions.Item>
              </Descriptions>

              {/* 运费模板信息 */}
              <Divider orientation="left">运费模板配置</Divider>
              <Row gutter={16}>
                <Col span={6}>
                  {renderTemplateInfo(
                    billingDetail.appliedFreightTemplates.generalTemplate,
                    ShippingTemplateType.GENERAL,
                    "普通货物模板"
                  )}
                </Col>
                <Col span={6}>
                  {renderTemplateInfo(
                    billingDetail.appliedFreightTemplates.batteryTemplate,
                    ShippingTemplateType.BATTERY,
                    "带电货物模板"
                  )}
                </Col>
                <Col span={6}>
                  {renderTemplateInfo(
                    billingDetail.appliedFreightTemplates.postBoxTemplate,
                    ShippingTemplateType.POST_BOX,
                    "投函货物模板"
                  )}
                </Col>
                <Col span={6}>
                  {renderTemplateInfo(
                    billingDetail.appliedFreightTemplates.specialTemplate,
                    ShippingTemplateType.SPECIAL,
                    "特殊货物模板"
                  )}
                </Col>
              </Row>
            </Card>

            {/* 账单明细 */}
            <Card
              title={
                <Space>
                  <EyeOutlined />
                  <span>账单明细</span>
                  <Badge count={itemsData.total} showZero color="#108ee9" />
                </Space>
              }
              extra={
                <Space>
                  <Button
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={() => loadBillingItems()}
                    loading={itemsLoading}
                  >
                    刷新明细
                  </Button>
                </Space>
              }
            >
              <Table
                columns={itemColumns}
                dataSource={itemsData.list}
                rowKey="id"
                loading={itemsLoading}
                pagination={{
                  current: currentPage,
                  pageSize: pageSize,
                  total: itemsData.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) =>
                    `第 ${range[0]}-${range[1]} 条，共 ${total} 条明细`,
                  pageSizeOptions: ["10", "20", "50", "100"],
                  onChange: handleTableChange,
                  onShowSizeChange: handleTableChange,
                }}
                scroll={{ x: 1500, y: 600 }}
                size="small"
                bordered
                rowClassName={(record, index) =>
                  index % 2 === 0 ? "table-row-light" : "table-row-dark"
                }
                locale={{
                  emptyText: (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无账单明细"
                    />
                  ),
                }}
                style={{
                  backgroundColor: "#fafafa",
                }}
              />
            </Card>
          </>
        ) : (
          !loading && (
            <div style={{ textAlign: "center", marginTop: "100px" }}>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="账单详情不存在或已被删除"
                children={
                  <Button type="primary" onClick={handleBack}>
                    返回列表
                  </Button>
                }
              />
            </div>
          )
        )}
      </Spin>

      {/* 运费调整记录弹窗 */}
      {billingDetail && (
        <BillingAdjustmentModal
          visible={adjustmentModalVisible}
          billingRecordId={billingRecordId}
          billNumber={billingDetail.billNumber}
          onClose={handleCloseAdjustmentModal}
        />
      )}
    </div>
  );
};

export default BillDetailPage;
