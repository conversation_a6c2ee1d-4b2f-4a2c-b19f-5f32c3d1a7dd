/* 新增运费模板Modal样式 */

.modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  .ant-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #1890ff;
  }

  .ant-modal-body {
    padding: 24px;
  }
}

.form {
  .ant-form-item-label > label {
    font-weight: 500;
  }
}

.sectionTitle {
  color: #1890ff;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f2f5;
}

.formRow {
  display: flex;
  gap: 16px;
  width: 100%;
}

.formColumn {
  flex: 1;
}

.inputNumber {
  width: 100%;
}

.tooltip {
  .ant-tooltip-inner {
    background-color: #1890ff;
  }

  .ant-tooltip-arrow-content {
    background-color: #1890ff;
  }
}

/* 表单验证样式 */
.ant-form-item-has-error .ant-input,
.ant-form-item-has-error .ant-input-number,
.ant-form-item-has-error .ant-select-selector {
  border-color: #ff4d4f;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.ant-form-item-has-error .ant-form-item-explain-error {
  font-size: 12px;
}

/* 成功状态样式 */
.ant-form-item-has-success .ant-input,
.ant-form-item-has-success .ant-input-number-input,
.ant-form-item-has-success .ant-select-selector {
  border-color: #52c41a;
}

/* 输入框聚焦样式 */
.ant-input:focus,
.ant-input-focused,
.ant-input-number-focused,
.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .formRow {
    flex-direction: column;
    gap: 0;
  }

  .modal {
    .ant-modal-body {
      padding: 16px;
    }
  }
}
