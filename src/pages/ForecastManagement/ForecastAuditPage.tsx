import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Table,
  Form,
  Input,
  Button,
  Space,
  DatePicker,
  Select,
  Row,
  Col,
  Drawer,
  Badge,
  Tag,
  Tooltip,
  message,
  Popconfirm,
  Divider,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  MinusCircleOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import type { TableColumnsType, TablePaginationConfig } from "antd";
import {
  getPendingManifests,
  updateManifest,
  approveManifest,
  ManifestInfo,
  ManifestQueryParams,
  ERROR_CODES,
} from "../../services/forecastService";
import { ApiError } from "../../services/apiClient";

const { RangePicker } = DatePicker;

// 使用索引签名来解决类型问题
interface ValidateStatusMapType {
  [key: number]: { color: string; text: string };
}

// 定义表单值的类型
interface SearchFormValues {
  expressNumber?: string;
  orderNumber?: string;
  validateStatus?: number;
  dateRange?: {
    format: (formatString: string) => string;
  }[];
  [key: string]: unknown;
}

// 映射验证状态到标签
const validateStatusMap: ValidateStatusMapType = {
  0: { color: "default", text: "未验证" },
  1: { color: "success", text: "验证通过" },
  2: { color: "error", text: "验证失败" },
};

const ForecastAuditPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<ManifestInfo[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [drawerVisible, setDrawerVisible] = useState<boolean>(false);
  const [currentManifest, setCurrentManifest] = useState<ManifestInfo | null>(
    null
  );
  const [windowWidth, setWindowWidth] = useState<number>(window.innerWidth);
  const [searchForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [errorInfo, setErrorInfo] = useState<{
    visible: boolean;
    message: string;
    field?: string;
  }>({
    visible: false,
    message: "",
  });
  const errorInfoRef = useRef<HTMLDivElement>(null);
  const zipCodeRef = useRef<HTMLDivElement>(null);
  const phoneRef = useRef<HTMLDivElement>(null);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // 根据窗口大小决定表格列的显示
  const getResponsiveColumns = () => {
    const baseColumns = [...columns]; // 创建列定义的副本

    if (windowWidth < 768) {
      // 移动设备视图，只显示关键列
      return baseColumns.filter((col) =>
        ["status", "expressNumber", "receiverName", "action"].includes(
          col.key as string
        )
      );
    } else if (windowWidth < 992) {
      // 平板视图，隐藏部分次要列
      return baseColumns.filter(
        (col) => !["orderNumber", "receiverZipCode"].includes(col.key as string)
      );
    }

    return baseColumns; // 桌面视图显示所有列
  };

  // 设置字段定位函数
  const scrollToField = (fieldName: string) => {
    // 根据字段名称决定滚动到哪个区域
    if (fieldName === "receiverZipCode" && zipCodeRef.current) {
      zipCodeRef.current.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
      // 添加闪烁效果
      const className = "highlight-field";
      zipCodeRef.current.classList.add(className);
      setTimeout(() => {
        zipCodeRef.current?.classList.remove(className);
      }, 2000);
    } else if (fieldName === "receiverPhone" && phoneRef.current) {
      phoneRef.current.scrollIntoView({ behavior: "smooth", block: "center" });
      // 添加闪烁效果
      const className = "highlight-field";
      phoneRef.current.classList.add(className);
      setTimeout(() => {
        phoneRef.current?.classList.remove(className);
      }, 2000);
    }
  };

  // 加载数据函数
  const loadData = async (params: ManifestQueryParams = {}) => {
    try {
      setLoading(true);
      const queryParams = {
        ...params,
        page: params.page || current,
        pageSize: params.pageSize || pageSize,
      };

      const result = await getPendingManifests(queryParams);
      setData(result.list || []);
      setTotal(result.total || 0);
    } catch (error) {
      console.error("加载预报数据失败:", error);
      message.error("加载预报数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 首次加载和分页/排序变化时加载数据
  useEffect(() => {
    loadData({ page: current, pageSize });
  }, [current, pageSize]);

  // 表格分页变化处理
  const handleTableChange = (pagination: TablePaginationConfig) => {
    if (pagination.current) {
      setCurrent(pagination.current);
    }
    if (pagination.pageSize) {
      setPageSize(pagination.pageSize);
    }
  };

  // 搜索处理
  const handleSearch = (values: SearchFormValues) => {
    const { dateRange, ...rest } = values;

    const params: ManifestQueryParams = {
      ...(rest as Partial<ManifestQueryParams>),
      page: 1,
    };

    // 处理日期范围
    if (dateRange && dateRange.length === 2) {
      params.startTime = dateRange[0].format("YYYY-MM-DD HH:mm:ss");
      params.endTime = dateRange[1].format("YYYY-MM-DD HH:mm:ss");
    }

    setCurrent(1); // 重置回第一页
    loadData(params);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setCurrent(1);
    loadData({ page: 1, pageSize });
  };

  // 打开编辑抽屉
  const handleEdit = (record: ManifestInfo) => {
    setCurrentManifest(record);
    // 设置表单初始值
    editForm.setFieldsValue({
      expressNumber: record.expressNumber,
      orderNumber: record.orderNumber,
      receiverZipCode: record.receiverZipCode,
      receiverName: record.receiverName,
      receiverAddress: record.receiverAddress,
      receiverPhone: record.receiverPhone,
      items: record.items,
    });
    setDrawerVisible(true);
  };

  // 关闭抽屉
  const handleCloseDrawer = () => {
    setDrawerVisible(false);
    setCurrentManifest(null);
    editForm.resetFields();
    setErrorInfo({ visible: false, message: "" });
  };

  // 保存修改
  const handleSave = async () => {
    try {
      const values = await editForm.validateFields();
      if (!currentManifest) return;

      setLoading(true);
      const result = await updateManifest(currentManifest.id, values);

      if (result && result.id) {
        message.success("保存成功");
        setDrawerVisible(false);
        await loadData({ page: current, pageSize });
      }
    } catch (error: unknown) {
      console.error("保存预报数据失败:", error);

      // 设置全局错误状态
      setErrorInfo({
        visible: true,
        message: "保存失败，请检查表单中标红的字段",
      });

      // 处理特定错误码
      if (error instanceof ApiError) {
        switch (error.errorCode) {
          case ERROR_CODES.ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID:
            message.error("邮编格式无效，请检查邮编格式");
            editForm.setFields([
              {
                name: "receiverZipCode",
                errors: ["邮政编码存在错误，请核实。"],
              },
            ]);
            setErrorInfo({
              visible: true,
              message: "邮编格式无效，请检查收件人信息中的邮编格式",
              field: "receiverZipCode",
            });
            setTimeout(() => scrollToField("receiverZipCode"), 100);
            break;
          case ERROR_CODES.ERROR_MANIFEST_ZIPCODE_NOT_EXIST:
            message.error("邮编不存在，请检查邮编是否正确");
            editForm.setFields([
              {
                name: "receiverZipCode",
                errors: ["邮政编码存在错误，请核实。"],
              },
            ]);
            setErrorInfo({
              visible: true,
              message: "邮编不存在于系统中，请核对邮编信息",
              field: "receiverZipCode",
            });
            setTimeout(() => scrollToField("receiverZipCode"), 100);
            break;
          case ERROR_CODES.ERROR_MANIFEST_PHONE_FORMAT_INVALID:
            message.error("电话号码格式无效，请检查电话格式");
            editForm.setFields([
              {
                name: "receiverPhone",
                errors: ["电话号码存在错误，请核实。"],
              },
            ]);
            setErrorInfo({
              visible: true,
              message: "电话号码格式无效，请检查收件人电话格式",
              field: "receiverPhone",
            });
            setTimeout(() => scrollToField("receiverPhone"), 100);
            break;
          default:
            message.error("保存失败，请检查表单数据");
            setErrorInfo({
              visible: true,
              message: error.message || "保存失败，请检查表单数据",
            });
            break;
        }
      } else {
        message.error("保存失败，请检查表单数据");
        setErrorInfo({
          visible: true,
          message: "系统错误，请稍后再试",
        });
      }

      // 滚动到错误提示区域
      if (errorInfoRef.current) {
        errorInfoRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
      setLoading(false);
    }
  };

  // 通过审核
  const handleApprove = async (id: number) => {
    try {
      setLoading(true);
      await approveManifest(id);
      message.success("审核通过成功");
      await loadData({ page: current, pageSize });
    } catch (error) {
      console.error("审核通过失败:", error);
      message.error("审核通过失败");
    } finally {
      setLoading(false);
    }
  };

  // 处理保存并通过
  const handleSaveAndApprove = async () => {
    try {
      const values = await editForm.validateFields();
      if (!currentManifest) return;

      setLoading(true);

      // 首先调用编辑接口
      try {
        // 更新预报单信息
        const result = await updateManifest(currentManifest.id, values);

        if (result && result.id) {
          // 不管之前的状态如何，都尝试调用通过审核接口
          try {
            await approveManifest(result.id);
            message.success("保存并通过审核成功");
            setDrawerVisible(false);
            await loadData({ page: current, pageSize });
            return;
          } catch (approveError) {
            console.error("审核通过失败:", approveError);
            message.error("审核通过失败");
            setLoading(false);
          }
        }
      } catch (error: unknown) {
        // 编辑接口报错处理，同handleSave中的错误处理逻辑
        console.error("保存预报数据失败:", error);

        // 设置全局错误状态
        setErrorInfo({
          visible: true,
          message: "保存失败，请检查表单中标红的字段",
        });

        if (error instanceof ApiError) {
          // 复用之前的错误处理逻辑
          switch (error.errorCode) {
            case ERROR_CODES.ERROR_MANIFEST_ZIPCODE_FORMAT_INVALID:
              message.error("邮编格式无效，请检查邮编格式");
              editForm.setFields([
                {
                  name: "receiverZipCode",
                  errors: ["邮政编码存在错误，请核实。"],
                },
              ]);
              setErrorInfo({
                visible: true,
                message: "邮编格式无效，请检查收件人信息中的邮编格式",
                field: "receiverZipCode",
              });
              setTimeout(() => scrollToField("receiverZipCode"), 100);
              break;
            case ERROR_CODES.ERROR_MANIFEST_ZIPCODE_NOT_EXIST:
              message.error("邮编不存在，请检查邮编是否正确");
              editForm.setFields([
                {
                  name: "receiverZipCode",
                  errors: ["邮政编码存在错误，请核实。"],
                },
              ]);
              setErrorInfo({
                visible: true,
                message: "邮编不存在于系统中，请核对邮编信息",
                field: "receiverZipCode",
              });
              setTimeout(() => scrollToField("receiverZipCode"), 100);
              break;
            case ERROR_CODES.ERROR_MANIFEST_PHONE_FORMAT_INVALID:
              message.error("电话号码格式无效，请检查电话格式");
              editForm.setFields([
                {
                  name: "receiverPhone",
                  errors: ["电话号码存在错误，请核实。"],
                },
              ]);
              setErrorInfo({
                visible: true,
                message: "电话号码格式无效，请检查收件人电话格式",
                field: "receiverPhone",
              });
              setTimeout(() => scrollToField("receiverPhone"), 100);
              break;
            default:
              message.error("保存失败，请检查表单数据");
              setErrorInfo({
                visible: true,
                message: error.message || "保存失败，请检查表单数据",
              });
              break;
          }
        } else {
          message.error("保存失败，请检查表单数据");
          setErrorInfo({
            visible: true,
            message: "系统错误，请稍后再试",
          });
        }

        if (errorInfoRef.current) {
          errorInfoRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
        setLoading(false);
      }
    } catch (formError) {
      // 表单验证失败
      console.error("表单验证失败:", formError);
      message.error("表单填写有误，请检查");
      setLoading(false);
    }
  };

  // 表格列定义
  const columns: TableColumnsType<ManifestInfo> = [
    {
      title: "状态",
      key: "status",
      width: 80,
      render: (_, record) => {
        // 确保 validateStatus 是有效值（0, 1, 2）
        const status =
          validateStatusMap[record.validateStatus] || validateStatusMap[0];
        return (
          <Badge
            status={
              status.color as
                | "success"
                | "error"
                | "default"
                | "processing"
                | "warning"
                | undefined
            }
            text={status.text}
          />
        );
      },
    },
    {
      title: "运单号",
      dataIndex: "expressNumber",
      key: "expressNumber",
      width: 120,
    },
    {
      title: "商家订单号",
      dataIndex: "orderNumber",
      key: "orderNumber",
      width: 120,
    },
    {
      title: "所属用户",
      dataIndex: "userNickname",
      key: "userNickname",
      width: 100,
    },
    {
      title: "收件人",
      dataIndex: "receiverName",
      key: "receiverName",
      width: 100,
    },
    {
      title: "收件人电话",
      dataIndex: "receiverPhone",
      key: "receiverPhone",
      width: 120,
      render: (text, record) => {
        const hasPhoneError =
          record.validateError && /电话|手机/.test(record.validateError);
        return (
          <span className={hasPhoneError ? "error-field" : ""}>
            {text}
            {hasPhoneError && (
              <Tooltip title={record.validateError}>
                <WarningOutlined
                  style={{ color: "red", marginLeft: 4 }}
                  className="error-icon"
                />
              </Tooltip>
            )}
          </span>
        );
      },
    },
    {
      title: "收件地址",
      dataIndex: "receiverAddress",
      key: "receiverAddress",
      width: 180,
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: "邮编",
      dataIndex: "receiverZipCode",
      key: "receiverZipCode",
      width: 100,
      render: (text, record) => {
        const hasZipError =
          record.validateError && /邮编/.test(record.validateError);
        return (
          <span className={hasZipError ? "error-field" : ""}>
            {text}
            {hasZipError && (
              <Tooltip title={record.validateError}>
                <WarningOutlined
                  style={{ color: "red", marginLeft: 4 }}
                  className="error-icon"
                />
              </Tooltip>
            )}
          </span>
        );
      },
    },
    {
      title: "预报时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
    },
    {
      title: "操作",
      key: "action",
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title={
              record.validateStatus === 2 ? (
                <div>
                  <div style={{ marginBottom: 8 }}>此预报单校验未通过</div>
                  <div style={{ color: "#ff4d4f", marginBottom: 16 }}>
                    {record.validateError || "未知错误"}
                  </div>
                  <div>确认强制通过此预报单？</div>
                </div>
              ) : (
                "确认通过此预报审核？"
              )
            }
            onConfirm={() => handleApprove(record.id)}
            okText="确认通过"
            cancelText="取消"
            disabled={record.validateStatus === 0}
            icon={
              <WarningOutlined
                style={{
                  color: record.validateStatus === 2 ? "#ff4d4f" : "#faad14",
                }}
              />
            }
            okButtonProps={{
              danger: record.validateStatus === 2,
              type: record.validateStatus === 2 ? "primary" : "default",
            }}
          >
            <Button
              type={record.validateStatus === 1 ? "primary" : "default"}
              size="small"
              icon={<CheckCircleOutlined />}
              disabled={record.validateStatus === 0}
              danger={record.validateStatus === 2}
              style={{
                backgroundColor:
                  record.validateStatus === 1 ? "#52c41a" : undefined,
                borderColor:
                  record.validateStatus === 1 ? "#52c41a" : undefined,
              }}
            >
              通过
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Card title={<span>预报审核</span>} bordered={false}>
        {/* 搜索表单 */}
        <Form
          form={searchForm}
          layout="horizontal"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={6}>
              <Form.Item label="运单号" name="expressNumber">
                <Input placeholder="请输入运单号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={6}>
              <Form.Item label="商家订单号" name="orderNumber">
                <Input placeholder="请输入商家订单号" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={6}>
              <Form.Item label="状态" name="validateStatus">
                <Select
                  placeholder="选择验证状态"
                  allowClear
                  options={[
                    { value: 0, label: "未验证" },
                    { value: 1, label: "验证通过" },
                    { value: 2, label: "验证失败" },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={6}>
              <Form.Item label="预报时间" name="dateRange">
                <RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} style={{ textAlign: "right" }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                >
                  搜索
                </Button>
                <Button onClick={handleReset} icon={<ReloadOutlined />}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>

        {/* 数据表格 */}
        <Table
          columns={getResponsiveColumns()}
          dataSource={data}
          rowKey="id"
          pagination={{
            current,
            pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            responsive: true,
          }}
          onChange={handleTableChange}
          loading={loading}
          scroll={{ x: "max-content" }}
          bordered
          size="middle"
        />
      </Card>

      {/* 编辑预报抽屉 */}
      <Drawer
        title={
          currentManifest
            ? `编辑预报单 - ${currentManifest.expressNumber}`
            : "编辑预报单"
        }
        width={windowWidth > 768 ? (windowWidth > 1200 ? 720 : "60%") : "100%"}
        placement="right"
        onClose={handleCloseDrawer}
        open={drawerVisible}
        bodyStyle={{ paddingBottom: 80 }}
        destroyOnClose={true}
        footer={
          <div style={{ textAlign: "right" }}>
            <Space>
              <Button onClick={handleCloseDrawer}>取消</Button>
              <Button
                type="primary"
                onClick={handleSave}
                loading={loading}
                icon={<SaveOutlined />}
              >
                保存修改
              </Button>
              <Button
                type="primary"
                onClick={handleSaveAndApprove}
                loading={loading}
                icon={<CheckCircleOutlined />}
                style={{ backgroundColor: "#52c41a", borderColor: "#52c41a" }}
              >
                保存并通过
              </Button>
            </Space>
          </div>
        }
      >
        {/* 错误信息提示区 */}
        {errorInfo.visible && (
          <div
            ref={errorInfoRef}
            style={{
              padding: "12px 16px",
              backgroundColor: "#fff2f0",
              border: "1px solid #ffccc7",
              borderRadius: "4px",
              marginBottom: 16,
              display: "flex",
              alignItems: "center",
              boxShadow: "0 2px 8px rgba(255, 77, 79, 0.2)",
              animation: "fadeIn 0.3s ease-out",
            }}
          >
            <WarningOutlined
              style={{ color: "#ff4d4f", fontSize: 20, marginRight: 12 }}
            />
            <div>
              <div
                style={{
                  fontWeight: "bold",
                  fontSize: 16,
                  marginBottom: 4,
                  color: "#ff4d4f",
                }}
              >
                保存失败
              </div>
              <div>{errorInfo.message}</div>
              {errorInfo.field && (
                <Button
                  type="link"
                  size="small"
                  style={{ padding: "0", marginTop: 4 }}
                  onClick={() => scrollToField(errorInfo.field || "")}
                >
                  点击定位错误字段
                </Button>
              )}
            </div>
          </div>
        )}

        {currentManifest && currentManifest.validateStatus === 2 && (
          <div style={{ marginBottom: 16 }}>
            <Tag color="red" icon={<WarningOutlined />}>
              {currentManifest.validateError}
            </Tag>
          </div>
        )}
        <Form form={editForm} layout="vertical">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                name="expressNumber"
                label="运单号"
                rules={[{ required: true, message: "请输入运单号" }]}
              >
                <Input placeholder="运单号" disabled />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                name="orderNumber"
                label="商家订单号"
                rules={[{ required: true, message: "请输入商家订单号" }]}
              >
                <Input placeholder="商家订单号" disabled />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">收件人信息</Divider>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12}>
              <Form.Item
                name="receiverName"
                label="收件人姓名"
                rules={[{ required: true, message: "请输入收件人姓名" }]}
              >
                <Input placeholder="收件人姓名" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12}>
              <div ref={phoneRef} className="field-container">
                <Form.Item
                  name="receiverPhone"
                  label="收件人电话"
                  rules={[{ required: true, message: "请输入收件人电话" }]}
                >
                  <Input placeholder="收件人电话" />
                </Form.Item>
              </div>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <div ref={zipCodeRef} className="field-container">
                <Form.Item
                  name="receiverZipCode"
                  label="邮政编码"
                  rules={[{ required: true, message: "请输入邮政编码" }]}
                >
                  <Input placeholder="邮政编码" />
                </Form.Item>
              </div>
            </Col>
            <Col xs={24} sm={12} md={16}>
              <Form.Item
                name="receiverAddress"
                label="详细地址"
                rules={[{ required: true, message: "请输入详细地址" }]}
                validateStatus={
                  currentManifest?.validateError?.includes("地址")
                    ? "error"
                    : undefined
                }
                help={
                  currentManifest?.validateError?.includes("地址")
                    ? "详细地址存在错误，请核实。"
                    : undefined
                }
              >
                <Input placeholder="详细地址" />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">物品信息</Divider>

          <Form.List name="items">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    size="small"
                    style={{ marginBottom: 16 }}
                    extra={
                      fields.length > 1 ? (
                        <Button
                          danger
                          icon={<MinusCircleOutlined />}
                          onClick={() => remove(name)}
                          size="small"
                        />
                      ) : null
                    }
                  >
                    <Row gutter={[16, 16]}>
                      <Col xs={24} sm={24} md={12}>
                        <Form.Item
                          {...restField}
                          name={[name, "name"]}
                          label="物品名称"
                          rules={[
                            { required: true, message: "请输入物品名称" },
                          ]}
                        >
                          <Input placeholder="物品名称" />
                        </Form.Item>
                      </Col>
                      <Col xs={24} sm={8} md={4}>
                        <Form.Item
                          {...restField}
                          name={[name, "quantity"]}
                          label="数量"
                          rules={[{ required: true, message: "请输入数量" }]}
                        >
                          <Input type="number" min={1} placeholder="数量" />
                        </Form.Item>
                      </Col>
                      <Col xs={12} sm={8} md={4}>
                        <Form.Item
                          {...restField}
                          name={[name, "weight"]}
                          label="重量(g)"
                          rules={[{ required: true, message: "请输入重量" }]}
                        >
                          <Input type="number" min={1} placeholder="重量" />
                        </Form.Item>
                      </Col>
                      <Col xs={12} sm={8} md={4}>
                        <Form.Item
                          {...restField}
                          name={[name, "price"]}
                          label="价格"
                          rules={[{ required: true, message: "请输入价格" }]}
                        >
                          <Input type="number" min={0} placeholder="价格" />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Card>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() =>
                      add({ name: "", weight: 0, quantity: 1, price: 0 })
                    }
                    block
                    icon={<PlusOutlined />}
                  >
                    添加物品
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </Drawer>

      <style>{`
        .field-container {
          transition: all 0.3s ease;
        }
        
        .highlight-field {
          background-color: #fffbe6;
          padding: 8px;
          border: 1px solid #ffe58f;
          border-radius: 4px;
          animation: pulse 1.5s ease;
        }
        
        @keyframes pulse {
          0% { box-shadow: 0 0 0 0 rgba(255, 177, 31, 0.7); }
          50% { box-shadow: 0 0 0 10px rgba(255, 177, 31, 0); }
          100% { box-shadow: 0 0 0 0 rgba(255, 177, 31, 0); }
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .error-field {
          color: red;
        }

        .error-icon {
          color: red;
        }
      `}</style>
    </>
  );
};

export default ForecastAuditPage;
