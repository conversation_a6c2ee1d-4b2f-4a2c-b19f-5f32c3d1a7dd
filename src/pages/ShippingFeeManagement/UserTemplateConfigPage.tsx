import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Select,
  Typography,
  Space,
  Button,
  Spin,
  Row,
  Col,
  Form,
  Input,
  List,
  Pagination,
  Avatar,
  Alert,
  App,
} from "antd";
import {
  SaveOutlined,
  ReloadOutlined,
  SearchOutlined,
  UserOutlined,
  PlusOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import {
  getActiveShipmentTypes,
  getShippingFeeTemplates,
  getUserTemplateConfigurations,
  updateUserTemplateConfigurations,
  getTemplateTypeFromShipmentType,
  ShipmentType,
  ShippingFeeTemplate,
  UserTemplateConfiguration,
  UserTemplateConfigItem,
} from "../../services/shippingFeeService";
import { fetchUserOptions } from "../../services/userService";
import CreateTemplateModal from "../../components/ShippingFeeTemplate/CreateTemplateModal";
import styles from "./UserTemplateConfigPage.module.css";

const { Title, Text } = Typography;
const { Option } = Select;

// 用户选项接口
interface UserOption {
  id: number;
  username: string;
  nickname?: string;
  email?: string;
}

/**
 * 用户模板配置页面
 */
const UserTemplateConfigPage: React.FC = () => {
  const { message } = App.useApp();
  // 状态管理
  const [users, setUsers] = useState<UserOption[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>(
    undefined
  );
  const [selectedUser, setSelectedUser] = useState<UserOption | undefined>(
    undefined
  );
  const [shipmentTypes, setShipmentTypes] = useState<ShipmentType[]>([]);
  const [allTemplates, setAllTemplates] = useState<ShippingFeeTemplate[]>([]);

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);
  const [userSearchKeyword, setUserSearchKeyword] = useState("");

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalUsers, setTotalUsers] = useState(0);

  // 表单实例
  const [form] = Form.useForm();

  // 新增模板相关状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [selectedTypeForCreate, setSelectedTypeForCreate] = useState<
    number | undefined
  >(undefined);

  // 搜索防抖相关状态
  const searchTimeoutRef = useRef<number | null>(null);

  // 未保存更改检测相关状态
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [initialFormValues, setInitialFormValues] = useState<
    Record<string, number | undefined>
  >({});

  // 检查是否有未保存的更改
  const checkForChanges = (
    currentValues: Record<string, number | undefined>
  ) => {
    const currentKeys = Object.keys(currentValues).filter(
      (key) => currentValues[key] !== undefined
    );
    const initialKeys = Object.keys(initialFormValues).filter(
      (key) => initialFormValues[key] !== undefined
    );

    // 比较键的数量和值
    if (currentKeys.length !== initialKeys.length) {
      return true;
    }

    return currentKeys.some(
      (key) => currentValues[key] !== initialFormValues[key]
    );
  };

  // 表单值变化监听
  const handleFormValuesChange = (
    _changedValues: Record<string, number | undefined>,
    allValues: Record<string, number | undefined>
  ) => {
    const hasChanges = checkForChanges(allValues);
    setHasUnsavedChanges(hasChanges);
  };

  // 页面离开确认提示
  const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    if (hasUnsavedChanges) {
      event.preventDefault();
      event.returnValue =
        "您有未保存的配置更改，确定要离开吗？离开后更改将丢失。";
      return event.returnValue;
    }
  };

  // 清空表单字段的辅助函数
  const clearFormFields = () => {
    const emptyValues: Record<string, undefined> = {};
    shipmentTypes.forEach((type) => {
      emptyValues[`template_${type.id}`] = undefined;
    });
    form.setFieldsValue(emptyValues);
    setInitialFormValues({});
    setHasUnsavedChanges(false);
  };

  // 获取用户列表
  const fetchUsers = async (keyword?: string, page = 1, size = 10) => {
    try {
      setUsersLoading(true);
      const response = await fetchUserOptions({
        keyword,
        page,
        pageSize: size,
      });
      setUsers(response.data?.list || []);
      setTotalUsers(response.data?.total || 0);
    } catch (error) {
      console.error("获取用户列表失败:", error);
      message.error("获取用户列表失败，请重试");
    } finally {
      setUsersLoading(false);
    }
  };

  // 获取货物类型列表
  const fetchShipmentTypes = async () => {
    try {
      const types = await getActiveShipmentTypes();
      setShipmentTypes(types);
    } catch (error) {
      console.error("获取货物类型失败:", error);
      message.error("获取货物类型失败，请重试");
    }
  };

  // 获取所有运费模板
  const fetchAllTemplates = async () => {
    try {
      // 获取所有模板，不提供搜索条件则返回所有模板
      const response = await getShippingFeeTemplates();
      setAllTemplates(response.templates);
    } catch (error) {
      console.error("获取运费模板失败:", error);
      message.error("获取运费模板失败，请重试");
    }
  };

  // 获取用户的模板配置
  const fetchUserConfigurations = async (userId: number) => {
    try {
      setLoading(true);
      const response = await getUserTemplateConfigurations(userId);

      // 创建货物类型到模板的映射关系
      const shipmentTypeToTemplateMap: Record<number, number> = {};

      // 遍历用户配置的模板，根据模板的type字段建立与货物类型的映射
      response.templates.forEach((template: UserTemplateConfigItem) => {
        // 查找模板类型对应的货物类型
        const matchedShipmentType = shipmentTypes.find((shipmentType) => {
          const expectedTemplateType =
            getTemplateTypeFromShipmentType(shipmentType);
          return expectedTemplateType === template.type;
        });

        if (matchedShipmentType) {
          shipmentTypeToTemplateMap[matchedShipmentType.id] = template.id;
        }
      });

      // 设置表单值
      const formValues: Record<string, number | undefined> = {};
      shipmentTypes.forEach((type) => {
        const templateId = shipmentTypeToTemplateMap[type.id];
        formValues[`template_${type.id}`] = templateId;
      });

      form.setFieldsValue(formValues);

      // 保存初始值用于比较变更
      setInitialFormValues({ ...formValues });
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("获取用户模板配置失败:", error);
      message.error("获取用户模板配置失败，请重试");
      // 出错时也要清空表单，避免显示错误的数据
      clearFormFields();
    } finally {
      setLoading(false);
    }
  };

  // 处理用户选择
  const handleUserSelect = (user: UserOption) => {
    // 如果有未保存更改，提示确认
    if (hasUnsavedChanges) {
      const confirmed = window.confirm(
        "当前有未保存的配置更改，切换用户后将丢失这些更改。确定要继续吗？"
      );
      if (!confirmed) {
        return;
      }
    }

    // 先清空表单，避免状态残留
    clearFormFields();

    setSelectedUserId(user.id);
    setSelectedUser(user);

    if (shipmentTypes.length > 0) {
      fetchUserConfigurations(user.id);
    }
  };

  // 处理用户搜索
  const handleUserSearch = () => {
    setCurrentPage(1); // 搜索时重置到第一页
    fetchUsers(userSearchKeyword, 1, pageSize);
  };

  // 防抖搜索函数
  const handleSearchKeywordChange = (value: string) => {
    setUserSearchKeyword(value);

    // 清除之前的定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 设置新的定时器，500ms后执行搜索
    searchTimeoutRef.current = setTimeout(() => {
      setCurrentPage(1); // 搜索时重置到第一页
      fetchUsers(value, 1, pageSize);
    }, 500);
  };

  // 清除搜索
  const handleClearSearch = () => {
    setUserSearchKeyword("");
    setCurrentPage(1);

    // 清除防抖定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // 重新加载所有用户
    fetchUsers("", 1, pageSize);
  };

  // 处理分页变化
  const handlePageChange = (page: number, size?: number) => {
    const newPageSize = size || pageSize;
    setCurrentPage(page);
    setPageSize(newPageSize);
    fetchUsers(userSearchKeyword, page, newPageSize);
  };

  // 保存配置
  const handleSaveConfigurations = async () => {
    if (!selectedUserId) {
      message.warning("请先选择用户");
      return;
    }

    try {
      setSaving(true);

      // 获取表单值
      const formValues = await form.validateFields();

      // 构建配置数据
      const configurations: UserTemplateConfiguration[] = [];

      shipmentTypes.forEach((type) => {
        const templateId = formValues[`template_${type.id}`];
        if (templateId) {
          // 获取模板对应的类型
          const templateType = getTemplateTypeFromShipmentType(type);

          configurations.push({
            type: templateType,
            templateId: templateId,
          });
        }
      });

      await updateUserTemplateConfigurations(selectedUserId, {
        configurations,
      });

      // 更新状态：清除未保存更改标记
      setHasUnsavedChanges(false);

      // 显示成功提示
      message.success(
        `用户 ${selectedUser?.username || ""}的模板配置保存成功！共配置了 ${
          configurations.length
        } 个模板。`
      );

      // 重新加载用户配置以确保数据同步
      await fetchUserConfigurations(selectedUserId);
    } catch (error) {
      console.error("保存用户模板配置失败:", error);
      message.error("保存用户模板配置失败，请重试");
    } finally {
      setSaving(false);
    }
  };

  // 重置配置
  const handleResetConfigurations = () => {
    if (selectedUserId) {
      // 先清空表单，然后重新加载配置
      clearFormFields();
      fetchUserConfigurations(selectedUserId);
    }
  };

  // 处理新增模板
  const handleCreateTemplate = (shipmentType?: ShipmentType) => {
    // 如果没有指定货物类型，则创建普通模板（类型1）
    const templateType = shipmentType
      ? getTemplateTypeFromShipmentType(shipmentType)
      : 1;
    // 先设置模板类型，然后在下一个事件循环中打开弹窗，确保状态更新完成
    setSelectedTypeForCreate(templateType);
    // 使用 setTimeout 确保状态更新完成后再打开弹窗
    setTimeout(() => {
      setCreateModalVisible(true);
    }, 0);
  };

  // 处理取消创建模板
  const handleCancelCreateTemplate = () => {
    setCreateModalVisible(false);
    setSelectedTypeForCreate(undefined);
  };

  // 处理创建模板成功
  const handleCreateTemplateSuccess = async (template: ShippingFeeTemplate) => {
    setCreateModalVisible(false);
    setSelectedTypeForCreate(undefined);

    try {
      // 重新获取所有模板列表
      await fetchAllTemplates();

      // 查找匹配的货物类型
      const matchedShipmentType = shipmentTypes.find(
        (shipmentType) =>
          getTemplateTypeFromShipmentType(shipmentType) ===
          selectedTypeForCreate
      );

      if (matchedShipmentType) {
        // 自动选中新创建的模板
        form.setFieldValue(`template_${matchedShipmentType.id}`, template.id);

        // 标记为有未保存更改，因为自动选中了新模板
        setHasUnsavedChanges(true);

        message.success(`运费模板 "${template.name}" 创建成功并已自动选中`);
      } else {
        message.success(`运费模板 "${template.name}" 创建成功`);
      }
    } catch (error) {
      console.error("刷新模板列表失败:", error);
      message.error("模板创建成功，但刷新列表失败，请手动刷新页面");
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchUsers();
    fetchShipmentTypes();
    fetchAllTemplates(); // 同时获取所有模板
  }, []);

  // 当用户和货物类型都加载完成后，如果有选中用户则加载其配置
  useEffect(() => {
    if (selectedUserId && shipmentTypes.length > 0) {
      fetchUserConfigurations(selectedUserId);
    }
  }, [selectedUserId, shipmentTypes]);

  // 设置页面离开监听
  useEffect(() => {
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // 清理搜索防抖定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      <Title level={4}>用户模板配置</Title>

      <Row gutter={24} style={{ marginTop: 16 }}>
        {/* 客户选择区 */}
        <Col span={8}>
          <Card title="客户选择" className={styles.userSelectionCard}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <Input.Search
                placeholder="搜索客户名称或邮箱"
                value={userSearchKeyword}
                onChange={(e) => handleSearchKeywordChange(e.target.value)}
                onSearch={handleUserSearch}
                onPressEnter={handleUserSearch}
                loading={usersLoading}
                enterButton={<SearchOutlined />}
                allowClear
                onClear={handleClearSearch}
              />

              <Spin spinning={usersLoading}>
                <List
                  className={styles.userList}
                  dataSource={users}
                  renderItem={(user) => (
                    <List.Item
                      className={`${styles.userListItem} ${
                        selectedUserId === user.id
                          ? styles.selectedUserItem
                          : ""
                      }`}
                      onClick={() => handleUserSelect(user)}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<UserOutlined />} />}
                        title={
                          <Space>
                            <Text strong>{user.username}</Text>
                            {user.nickname && (
                              <Text type="secondary">({user.nickname})</Text>
                            )}
                          </Space>
                        }
                        description={user.email}
                      />
                    </List.Item>
                  )}
                />
              </Spin>

              {/* 分页组件 */}
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalUsers}
                onChange={handlePageChange}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`
                }
                pageSizeOptions={["5", "10", "20", "50"]}
                size="small"
              />

              {selectedUser && (
                <Card size="small" className={styles.selectedUserCard}>
                  <Text strong>当前选中客户：</Text>
                  <br />
                  <Text>{selectedUser.username}</Text>
                  {selectedUser.nickname && (
                    <>
                      <br />
                      <Text type="secondary">{selectedUser.nickname}</Text>
                    </>
                  )}
                  {selectedUser.email && (
                    <>
                      <br />
                      <Text type="secondary" style={{ fontSize: "12px" }}>
                        {selectedUser.email}
                      </Text>
                    </>
                  )}
                </Card>
              )}
            </Space>
          </Card>
        </Col>

        {/* 模板配置区 */}
        <Col span={16}>
          <Card
            title={
              <Space>
                <Text>模板配置</Text>
                {hasUnsavedChanges && (
                  <Alert
                    message="有未保存的更改"
                    type="warning"
                    showIcon
                    icon={<ExclamationCircleOutlined />}
                    className={styles.unsavedChangesAlert}
                  />
                )}
              </Space>
            }
            className={styles.configCard}
            extra={
              selectedUserId && (
                <Space>
                  <Button
                    icon={<PlusOutlined />}
                    onClick={() => handleCreateTemplate()}
                    disabled={loading}
                  >
                    新增模板
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleResetConfigurations}
                    disabled={loading}
                  >
                    重置
                  </Button>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSaveConfigurations}
                    loading={saving}
                    disabled={loading}
                    danger={hasUnsavedChanges}
                  >
                    保存配置
                  </Button>
                </Space>
              )
            }
          >
            {!selectedUserId ? (
              <div className={styles.emptyState}>
                <Text type="secondary">请先选择客户</Text>
              </div>
            ) : (
              <Spin spinning={loading}>
                <Form
                  form={form}
                  layout="vertical"
                  onValuesChange={handleFormValuesChange}
                >
                  <Space
                    direction="vertical"
                    style={{ width: "100%" }}
                    size="large"
                  >
                    {shipmentTypes.map((type) => (
                      <Card
                        key={type.id}
                        size="small"
                        className={styles.typeCard}
                      >
                        <Row align="middle">
                          <Col span={6}>
                            <Text strong>{type.name}</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: "12px" }}>
                              类型代码: {type.code}
                            </Text>
                          </Col>
                          <Col span={18}>
                            <Form.Item
                              name={`template_${type.id}`}
                              label="运费模板"
                              style={{ marginBottom: 0 }}
                            >
                              <Select
                                placeholder="请选择运费模板"
                                style={{ width: "100%" }}
                                allowClear
                                showSearch
                                optionLabelProp="label"
                                filterOption={(input, option) => {
                                  const searchValue = input.toLowerCase();
                                  const templateName =
                                    (option?.label as string)?.toLowerCase() ||
                                    "";

                                  // 查找对应的模板对象以支持更多字段搜索
                                  const template = allTemplates.find(
                                    (t) => t.id === option?.value
                                  );
                                  if (!template) return false;

                                  // 支持按模板名称、首重价格、续重价格搜索
                                  return (
                                    templateName.includes(searchValue) ||
                                    template.firstWeightPrice
                                      .toString()
                                      .includes(searchValue) ||
                                    template.continuedWeightPrice
                                      .toString()
                                      .includes(searchValue)
                                  );
                                }}
                                notFoundContent="未找到匹配的模板"
                              >
                                {allTemplates.map((template) => (
                                  <Option
                                    key={template.id}
                                    value={template.id}
                                    label={template.name}
                                  >
                                    <div className={styles.templateOption}>
                                      <div className={styles.templateName}>
                                        <Text strong>{template.name}</Text>
                                      </div>
                                      <div className={styles.templateDetails}>
                                        <Text
                                          type="secondary"
                                          style={{ fontSize: "11px" }}
                                        >
                                          首重: ¥{template.firstWeightPrice}/
                                          {template.firstWeightRange}kg | 续重:
                                          ¥{template.continuedWeightPrice}/
                                          {template.continuedWeightInterval}
                                          kg | 轻抛:{" "}
                                          {template.bulkCoefficient.toLocaleString()}{" "}
                                          | 三边: {template.threeSidesStart}
                                          cm
                                        </Text>
                                      </div>
                                    </div>
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                  </Space>
                </Form>
              </Spin>
            )}
          </Card>
        </Col>
      </Row>

      {/* 新增模板弹窗 */}
      <CreateTemplateModal
        visible={createModalVisible}
        selectedTypeId={selectedTypeForCreate}
        onCancel={handleCancelCreateTemplate}
        onSuccess={handleCreateTemplateSuccess}
      />
    </>
  );
};

export default UserTemplateConfigPage;
