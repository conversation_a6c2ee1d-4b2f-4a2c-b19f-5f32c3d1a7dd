import React, { FC } from "react";
import {
  Table,
  Space,
  Button,
  Dropdown,
  MenuProps,
  Tooltip,
  Tag,
  Flex,
  Typography,
} from "antd";
import {
  EyeOutlined,
  PrinterOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  MoreOutlined,
  UserOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  MailOutlined,
  <PERSON>CircleOutlined,
  EditOutlined,
  CarOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import type { TablePaginationConfig } from "antd/es/table";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
  Manifest,
  MANIFEST_STATUS_MAP,
  ManifestStatus,
} from "../../services/manifestService";
import CopyableText from "../common/CopyableText";
import "../../styles/components/ManifestStyles.css";

const { Text } = Typography;

// 设置是否使用地址文本自动换行样式
const USE_ADDRESS_WRAP = true; // 保持为 true 以启用自动换行

interface ManifestTableProps {
  data: Manifest[];
  loading: boolean;
  total: number;
  current: number;
  pageSize: number;
  selectedRowKeys?: React.Key[];
  onSelectChange?: (selectedRowKeys: React.Key[]) => void;
  onPageChange: (page: number, pageSize: number) => void;
  onViewDetail: (id: number) => void;
  onPrint?: (id: number) => void;
  onUpdateTracking?: (id: number) => void;
  onMarkException?: (id: number) => void;
  onAdjustFee?: (id: number) => void; // 添加调整费用的回调函数
  onModifyTemplate?: (id: number) => void; // 添加修改模板的回调函数
  onViewTracking?: (id: number, expressNumber: string) => void; // 添加查看轨迹的回调函数
  onViewLog?: (id: number) => void; // 添加查看日志的回调函数
}

const ManifestTable: FC<ManifestTableProps> = ({
  data,
  loading,
  total,
  current,
  pageSize,
  selectedRowKeys,
  onSelectChange,
  onPageChange,
  onViewDetail,
  onPrint,
  onUpdateTracking,
  onMarkException,
  onAdjustFee,
  onModifyTemplate,
  onViewTracking,
  onViewLog,
}) => {
  // 确保data始终为数组，即使传入undefined或null
  const safeData = Array.isArray(data) ? data : [];

  // 表格分页配置
  const paginationProps: TablePaginationConfig = {
    current,
    pageSize,
    total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条记录`,
    onChange: onPageChange,
    pageSizeOptions: ["10", "20", "50", "100"],
    responsive: true,
  };

  // 获取状态标签颜色
  const getStatusTagColor = (status: ManifestStatus): string => {
    switch (status) {
      case ManifestStatus.PENDING_AUDIT:
        return "default";
      case ManifestStatus.FORECASTED:
        return "blue";
      case ManifestStatus.PICKED_UP:
        return "orange";
      case ManifestStatus.SHIPPED:
        return "green";
      case ManifestStatus.DELIVERED:
        return "purple";
      default:
        return "default";
    }
  };

  // 生成更多操作菜单
  const getMoreActionMenu = (record: Manifest): MenuProps => {
    const items: MenuProps["items"] = [];

    if (onPrint) {
      items.push({
        key: "print",
        label: "打印面单",
        icon: <PrinterOutlined />,
        onClick: () => onPrint(record.id),
      });
    }

    if (onUpdateTracking && record.status === ManifestStatus.SHIPPED) {
      items.push({
        key: "update",
        label: "更新状态",
        icon: <CheckCircleOutlined />,
        onClick: () => onUpdateTracking(record.id),
      });
    }

    if (onAdjustFee) {
      items.push({
        key: "adjustFee",
        label: "调整费用",
        icon: <DollarCircleOutlined />,
        onClick: () => onAdjustFee(record.id),
      });
    }

    // 修改模板功能：仅在非待审核和非已预报状态下显示
    if (
      onModifyTemplate &&
      record.status !== ManifestStatus.PENDING_AUDIT &&
      record.status !== ManifestStatus.FORECASTED
    ) {
      items.push({
        key: "modifyTemplate",
        label: "修改模板",
        icon: <EditOutlined />,
        onClick: () => onModifyTemplate(record.id),
      });
    }

    if (onViewTracking) {
      items.push({
        key: "viewTracking",
        label: "查看轨迹",
        icon: <CarOutlined />,
        onClick: () => onViewTracking(record.id, record.expressNumber),
      });
    }

    if (onMarkException) {
      items.push({
        key: "exception",
        label: "标记异常",
        icon: <ExclamationCircleOutlined />,
        danger: true,
        onClick: () => onMarkException(record.id),
      });
    }

    if (onViewLog) {
      items.push({
        key: "viewLog",
        label: "查看日志",
        icon: <FileTextOutlined />,
        onClick: () => onViewLog(record.id),
      });
    }

    return {
      items,
    };
  };

  // 定义表格列
  const columns: ColumnsType<Manifest> = [
    {
      title: "序号",
      key: "index",
      width: 50,
      render: (_, __, index) => (current - 1) * pageSize + index + 1,
    },
    {
      title: "单号详情",
      key: "orderNumbers",
      width: 200,
      render: (_: unknown, record: Manifest) => (
        <div style={{ display: "flex", flexDirection: "column", gap: "2px" }}>
          {record.expressNumber && (
            <Flex align="center">
              <Text className="order-label">运单:</Text>
              <CopyableText
                text={record.expressNumber}
                tooltip="复制运单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
          {record.orderNumber && (
            <Flex align="center">
              <Text className="order-label">商家:</Text>
              <CopyableText
                text={record.orderNumber}
                tooltip="复制商家订单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
          {record.orderNo && (
            <Flex align="center">
              <Text className="order-label">系统:</Text>
              <CopyableText
                text={record.orderNo}
                tooltip="复制系统订单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
          {record.transferredTrackingNumber && (
            <Flex align="center">
              <Text className="order-label">转单:</Text>
              <CopyableText
                text={record.transferredTrackingNumber}
                tooltip="复制转单号"
                className="wrappable-copyable-text"
              />
            </Flex>
          )}
        </div>
      ),
    },
    {
      title: "收件人信息",
      key: "receiver",
      width: 220,
      render: (_: unknown, record: Manifest) => (
        <div className="receiver-info">
          <Flex align="center" gap={4}>
            <UserOutlined style={{ color: "#555" }} />
            <span style={{ fontWeight: "bold" }}>{record.receiverName}</span>
          </Flex>

          <Flex align="center" gap={4}>
            <PhoneOutlined style={{ color: "#1890ff" }} />
            <CopyableText text={record.receiverPhone} tooltip="复制电话号码" />
          </Flex>

          {record.receiverZipCode && (
            <Flex align="center" gap={4}>
              <MailOutlined style={{ color: "#767676" }} />
              <CopyableText text={record.receiverZipCode} tooltip="复制邮编" />
            </Flex>
          )}

          <Flex align="center" gap={4}>
            <EnvironmentOutlined style={{ color: "#ff4d4f" }} />
            <div style={{ flex: 1, minWidth: 0 }}>
              <Tooltip title={record.receiverAddress} placement="topLeft">
                <div
                  className={
                    USE_ADDRESS_WRAP ? "address-text-wrap" : "address-text"
                  }
                >
                  {record.receiverAddress}
                </div>
              </Tooltip>
            </div>
          </Flex>
        </div>
      ),
    },
    {
      title: "状态与时间",
      key: "statusAndTime",
      width: 200,
      render: (_, record: Manifest) => {
        const statusInfo = MANIFEST_STATUS_MAP[
          record.status as keyof typeof MANIFEST_STATUS_MAP
        ] || {
          label: "未知",
          color: "default",
        };

        // 根据状态选择对应的时间显示
        let timeToShow = "";
        let timeLabel = "";

        switch (record.status) {
          case ManifestStatus.PENDING_AUDIT:
            timeToShow = record.createTime;
            timeLabel = "预报时间";
            break;
          case ManifestStatus.FORECASTED:
            timeToShow = record.createTime;
            timeLabel = "预报时间";
            break;
          case ManifestStatus.PICKED_UP:
            timeToShow = record.pickUpTime || record.createTime;
            timeLabel = record.pickUpTime ? "揽件时间" : "预报时间";
            break;
          case ManifestStatus.SHIPPED:
            timeToShow =
              record.shipmentTime || record.pickUpTime || record.createTime;
            timeLabel = record.shipmentTime
              ? "发货时间"
              : record.pickUpTime
              ? "揽件时间"
              : "预报时间";
            break;
          case ManifestStatus.DELIVERED:
            timeToShow =
              record.deliveredTime ||
              record.shipmentTime ||
              record.pickUpTime ||
              record.createTime;
            timeLabel = record.deliveredTime
              ? "送达时间"
              : record.shipmentTime
              ? "发货时间"
              : record.pickUpTime
              ? "揽件时间"
              : "预报时间";
            break;
          default:
            timeToShow = record.createTime;
            timeLabel = "预报时间";
            break;
        }

        // 构建完整的时间信息用于Tooltip
        const allTimeInfo = [];
        if (record.createTime) {
          allTimeInfo.push(
            `预报时间: ${dayjs(record.createTime).format(
              "YYYY-MM-DD HH:mm:ss"
            )}`
          );
        }
        if (record.pickUpTime) {
          allTimeInfo.push(
            `揽件时间: ${dayjs(record.pickUpTime).format(
              "YYYY-MM-DD HH:mm:ss"
            )}`
          );
        }
        if (record.shipmentTime) {
          allTimeInfo.push(
            `发货时间: ${dayjs(record.shipmentTime).format(
              "YYYY-MM-DD HH:mm:ss"
            )}`
          );
        }
        if (record.deliveredTime) {
          allTimeInfo.push(
            `送达时间: ${dayjs(record.deliveredTime).format(
              "YYYY-MM-DD HH:mm:ss"
            )}`
          );
        }

        const tooltipContent =
          allTimeInfo.length > 0 ? allTimeInfo.join("\n") : "暂无时间信息";

        return (
          <Tooltip
            title={
              <pre style={{ margin: 0, fontFamily: "inherit" }}>
                {tooltipContent}
              </pre>
            }
            placement="topLeft"
          >
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                gap: "4px",
                cursor: "help",
              }}
            >
              <Tag color={getStatusTagColor(record.status)}>
                {statusInfo.label}
              </Tag>
              {timeToShow && (
                <div style={{ fontSize: "12px", color: "#666" }}>
                  <div style={{ fontWeight: "500", marginBottom: "2px" }}>
                    {timeLabel}
                  </div>
                  <div>{dayjs(timeToShow).format("MM-DD HH:mm")}</div>
                  {allTimeInfo.length > 1 && (
                    <div
                      style={{
                        fontSize: "10px",
                        color: "#999",
                        marginTop: "2px",
                      }}
                    >
                      悬停查看全部时间 ↗
                    </div>
                  )}
                </div>
              )}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: "客户",
      dataIndex: "userNickname",
      key: "customer",
      width: 120,
    },
    {
      title: "操作",
      key: "action",
      width: 50,
      fixed: "right" as const,
      render: (_: unknown, record: Manifest) => {
        const menu = getMoreActionMenu(record);

        return (
          <Space size="small" className="action-buttons">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onViewDetail(record.id)}
            >
              详情
            </Button>

            {menu.items && menu.items.length > 0 && (
              <Dropdown menu={menu} placement="bottomRight">
                <Button type="text" size="small" icon={<MoreOutlined />} />
              </Dropdown>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <Table
      rowKey="id"
      dataSource={safeData}
      columns={columns}
      pagination={paginationProps}
      loading={loading}
      scroll={{ x: "max-content" }}
      rowSelection={
        onSelectChange
          ? {
              selectedRowKeys,
              onChange: onSelectChange,
            }
          : undefined
      }
      size="middle"
      bordered
      style={{ border: "1px solid #f0f0f0" }}
      rowClassName={(record, index) => {
        const classes = [];
        if (index % 2 === 1) {
          classes.push("zebra-stripe");
        }
        if (
          record.transferredTrackingNumber &&
          record.transferredTrackingNumber.trim() !== ""
        ) {
          classes.push("has-transferred-tracking-number");
        }
        return classes.join(" ");
      }}
    />
  );
};

export default ManifestTable;
