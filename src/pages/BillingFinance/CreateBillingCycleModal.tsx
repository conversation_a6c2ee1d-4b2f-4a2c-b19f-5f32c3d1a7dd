import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  message,
  Row,
  Col,
  Card,
  Tag,
  Space,
  Typography,
  Divider,
} from "antd";
import {
  createBillingCycle,
  CreateBillingCycleRequest,
  generateYearOptions,
  fetchBillingCycles,
  BillingCycle,
} from "../../services/billingCycleService";
import { PlusOutlined, CheckOutlined } from "@ant-design/icons";

const { Option } = Select;
const { TextArea } = Input;
const { Text, Title } = Typography;

interface CreateBillingCycleModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// 月份信息接口
interface MonthInfo {
  month: number;
  name: string;
  exists: boolean;
  billingCycle?: BillingCycle;
}

const CreateBillingCycleModal: React.FC<CreateBillingCycleModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedYear, setSelectedYear] = useState<number>(
    new Date().getFullYear()
  );
  const [monthsInfo, setMonthsInfo] = useState<MonthInfo[]>([]);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(null);
  const [loadingMonths, setLoadingMonths] = useState(false);

  // 月份名称映射
  const monthNames = [
    "1月",
    "2月",
    "3月",
    "4月",
    "5月",
    "6月",
    "7月",
    "8月",
    "9月",
    "10月",
    "11月",
    "12月",
  ];

  // 初始化月份信息
  const initializeMonthsInfo = (): MonthInfo[] => {
    return monthNames.map((name, index) => ({
      month: index + 1,
      name,
      exists: false,
    }));
  };

  // 加载指定年份的账单批次信息
  const loadYearBillingCycles = async (year: number) => {
    try {
      setLoadingMonths(true);
      const response = await fetchBillingCycles({
        cycleYear: year,
        page: 1,
        pageSize: 12, // 最多12个月
      });

      const existingCycles = response.list || [];
      const newMonthsInfo = initializeMonthsInfo();

      // 标记已存在的月份
      existingCycles.forEach((cycle: BillingCycle) => {
        const monthIndex = cycle.cycleMonth - 1;
        if (monthIndex >= 0 && monthIndex < 12) {
          newMonthsInfo[monthIndex].exists = true;
          newMonthsInfo[monthIndex].billingCycle = cycle;
        }
      });

      setMonthsInfo(newMonthsInfo);
    } catch (error) {
      console.error("加载年份账单批次失败:", error);
      message.error("加载年份信息失败");
      setMonthsInfo(initializeMonthsInfo());
    } finally {
      setLoadingMonths(false);
    }
  };

  // 年份变化时重新加载
  const handleYearChange = (year: number) => {
    setSelectedYear(year);
    setSelectedMonth(null);
    form.resetFields(["cycleName", "notes"]);
    loadYearBillingCycles(year);
  };

  // 选择月份
  const handleMonthSelect = (monthInfo: MonthInfo) => {
    if (monthInfo.exists) {
      message.info(`${selectedYear}年${monthInfo.name}的账单批次已存在`);
      return;
    }

    setSelectedMonth(monthInfo.month);
    // 自动生成账单批次名称
    const cycleName = `${selectedYear}年${monthInfo.month}月`;
    form.setFieldsValue({ cycleName });
  };

  // 在弹窗打开时初始化
  useEffect(() => {
    if (visible) {
      const currentYear = new Date().getFullYear();
      setSelectedYear(currentYear);
      setSelectedMonth(null);
      form.resetFields();
      loadYearBillingCycles(currentYear);
    }
  }, [visible, form]);

  /**
   * 处理表单提交
   */
  const handleSubmit = async () => {
    if (!selectedMonth) {
      message.warning("请选择要创建的月份");
      return;
    }

    try {
      const values = await form.validateFields();
      setLoading(true);

      const params: CreateBillingCycleRequest = {
        cycleYear: selectedYear,
        cycleMonth: selectedMonth,
        cycleName: values.cycleName,
        notes: values.notes,
      };

      await createBillingCycle(params);
      message.success("账单批次创建成功");
      form.resetFields();
      setSelectedMonth(null);
      onSuccess();
      onClose();
    } catch (error) {
      console.error("创建账单批次失败:", error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理取消
   */
  const handleCancel = () => {
    form.resetFields();
    setSelectedMonth(null);
    onClose();
  };

  return (
    <Modal
      title="创建账单批次"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
          disabled={!selectedMonth}
        >
          创建账单批次
        </Button>,
      ]}
      width={800}
      destroyOnClose
    >
      <div style={{ padding: "16px 0" }}>
        {/* 年份选择 */}
        <div style={{ marginBottom: "24px" }}>
          <Title level={5}>选择年份</Title>
          <Select
            value={selectedYear}
            onChange={handleYearChange}
            style={{ width: 200 }}
            loading={loadingMonths}
          >
            {generateYearOptions().map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </div>

        <Divider />

        {/* 月份选择 */}
        <div style={{ marginBottom: "24px" }}>
          <Title level={5}>选择月份</Title>
          <Text
            type="secondary"
            style={{ display: "block", marginBottom: "16px" }}
          >
            绿色表示已存在的账单批次，灰色表示可以创建的月份
          </Text>

          <Row gutter={[16, 16]}>
            {monthsInfo.map((monthInfo) => (
              <Col span={6} key={monthInfo.month}>
                <Card
                  size="small"
                  hoverable={!monthInfo.exists}
                  style={{
                    cursor: monthInfo.exists ? "not-allowed" : "pointer",
                    border:
                      selectedMonth === monthInfo.month
                        ? "2px solid #1890ff"
                        : undefined,
                    backgroundColor: monthInfo.exists ? "#f5f5f5" : undefined,
                  }}
                  onClick={() => handleMonthSelect(monthInfo)}
                  bodyStyle={{ padding: "12px", textAlign: "center" }}
                >
                  <Space
                    direction="vertical"
                    size="small"
                    style={{ width: "100%" }}
                  >
                    <Text strong>{monthInfo.name}</Text>
                    {monthInfo.exists ? (
                      <Tag color="success" icon={<CheckOutlined />}>
                        已存在
                      </Tag>
                    ) : (
                      <Tag color="default" icon={<PlusOutlined />}>
                        可创建
                      </Tag>
                    )}
                    {monthInfo.exists && monthInfo.billingCycle && (
                      <Text type="secondary" style={{ fontSize: "12px" }}>
                        {monthInfo.billingCycle.cycleName}
                      </Text>
                    )}
                  </Space>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {/* 选中月份的表单 */}
        {selectedMonth && (
          <>
            <Divider />
            <div>
              <Title level={5}>账单批次信息</Title>
              <Form form={form} layout="vertical">
                <Form.Item
                  name="cycleName"
                  label="账单批次名称"
                  rules={[
                    { required: true, message: "请输入账单批次名称" },
                    { max: 100, message: "账单批次名称不能超过100个字符" },
                  ]}
                >
                  <Input placeholder="请输入账单批次名称" />
                </Form.Item>

                <Form.Item
                  name="notes"
                  label="备注信息"
                  rules={[{ max: 500, message: "备注信息不能超过500个字符" }]}
                >
                  <TextArea
                    placeholder="请输入备注信息（可选）"
                    rows={3}
                    showCount
                    maxLength={500}
                  />
                </Form.Item>
              </Form>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default CreateBillingCycleModal;
